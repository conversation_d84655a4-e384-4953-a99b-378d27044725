-- ------------------------------------
--  表名称: modbus站点
--  适用数据库：MySql
--  表名称：modbus_site
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.08.05
-- ------------------------------------

DROP TABLE IF EXISTS modbus_site;
CREATE TABLE modbus_site (
  id                           bigint NOT NULL AUTO_INCREMENT    comment '主键ID',
  site_id                      bigint   NOT NULL                 comment '站点id',
  site_name                    varchar(64)   NOT NULL            comment '站点名称',
  description                  varchar(64)   DEFAULT NULL        comment '站点描述',
  master_id                    bigint        DEFAULT NULL        comment '主站点id',
  host                         varchar(64)   DEFAULT NULL        comment '主机地址',
  port                         int           DEFAULT NULL        comment '端口',
  keep_alive                   int           DEFAULT NULL        comment '是否长连接：0-否；1-是；',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除；',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (id)
) comment 'modbus站点';
