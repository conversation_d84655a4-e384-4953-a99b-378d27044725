-- ------------------------------------
--  表名称: modbus点位
--  适用数据库：MySql
--  表名称：modbus_points
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.08.05
-- ------------------------------------

DROP TABLE IF EXISTS modbus_points;
CREATE TABLE modbus_points (
  id                           bigint NOT NULL AUTO_INCREMENT    comment '点位 ID',
  point_name                   varchar(64)   NOT NULL            comment '点位名称',
  description                  varchar(64)   DEFAULT NULL        comment '点位描述',
  site_id                      bigint        DEFAULT NULL        comment '所属站点ID',
  master_id                    bigint        DEFAULT NULL        comment '主站ID',
  slave_id                     bigint        DEFAULT NULL        comment '从站ID',
  function_code                varchar(64)   DEFAULT NULL        comment '功能码：1-COIL_STATUS;2-INPUT_STATUS;3-HOLDING_REGISTER;4-INPUT_REGISTER',
  register_address             int           DEFAULT NULL        comment '寄存器地址',
  status                       int           DEFAULT NULL        comment '状态：0-禁用；1-启用;',
  auto_read                    int           DEFAULT NULL        comment '自动读取：0-不读取；1-自动读取;',
  `interval`                   int           DEFAULT NULL        comment '采集间隔，单位毫秒',
  data_type                    varchar(20)   DEFAULT NULL        comment '数据类型:2-TWO_BYTE_INT_UNSIGNED;3-TWO_BYTE_INT_SIGNED;',
  mapping_config               varchar(4096) DEFAULT NULL        comment '映射键名称，json。支持对象和path',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除;',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (id)
) comment 'modbus点位';
