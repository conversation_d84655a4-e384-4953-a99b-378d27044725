-- ------------------------------------
--  表名称:iot设备表
--  适用数据库：MySql
--  表名称：iot_device
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.01.07
-- ------------------------------------

DROP TABLE IF EXISTS iot_device;
CREATE TABLE iot_device (
  id                           bigint NOT NULL AUTO_INCREMENT    comment '设备 ID',
  device_key                   varchar(64)   NOT NULL            comment '设备唯一标识符(全局唯一，用于识别设备)',
  device_name                  varchar(64)   NOT NULL            comment '设备名称(在产品内唯一，用于标识设备)',
  nickname                     varchar(64)   NOT NULL            comment '设备备注名称',
  serial_number                varchar(64)   DEFAULT NULL        comment '设备序列号',
  product_id                   bigint        DEFAULT NULL        comment '产品编号',
  product_key                  varchar(64)   DEFAULT NULL        comment '产品标识',
  device_type                  varchar(2)    DEFAULT NULL        comment '设备类型:0-直连设备;1-网关子设备;2-网关设备;',
  status                       varchar(2)    DEFAULT NULL        comment '设备状态:0-未激活;1-在线;2-离线;3-已禁用;',
  gateway_id                   bigint        DEFAULT NULL        comment '网关设备编号(子设备需要关联的网关设备 ID)',
  status_last_update_time      datetime      DEFAULT NULL        comment '设备状态最后更新时间',
  last_online_time             datetime      DEFAULT NULL        comment '最后上线时间',
  last_offline_time            datetime      DEFAULT NULL        comment '最后离线时间',
  active_time                  datetime      DEFAULT NULL        comment '设备激活时间',
  ip                           varchar(64)   DEFAULT NULL        comment '设备的IP地址',
  firmware_version             varchar(32)   DEFAULT NULL        comment '设备的固件版本',
  device_secret                varchar(128)  DEFAULT NULL        comment '设备密钥(用于设备认证,需安全存储)',
  mqtt_client_id               varchar(64)   DEFAULT NULL        comment 'MQTT客户端ID',
  mqtt_username                varchar(32)   DEFAULT NULL        comment 'MQTT用户名',
  mqtt_password                varchar(64)   DEFAULT NULL        comment 'MQTT密码',
  auth_type                    varchar(2)    DEFAULT NULL        comment '认证类型:1-一机一密;2-动态注册;3-预注册',
  latitude                     varchar(64)   DEFAULT NULL        comment '设备位置的纬度',
  longitude                    varchar(64)   DEFAULT NULL        comment '设备位置的经度',
  area_id                      varchar(16)   DEFAULT NULL        comment '地区编码(关联Area的id)',
  address                      varchar(64)   DEFAULT NULL        comment '设备详细地址',
  extend_config                varchar(4096) DEFAULT NULL        comment '扩展配置',
  task_config                  varchar(4096) DEFAULT NULL        comment '任务配置',
      cust_id                      varchar(32)   DEFAULT NULL        comment '客户编号',
  device_sort                  int           DEFAULT NULL        comment '设备排序',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除；',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (id)
) comment 'iot设备表';
