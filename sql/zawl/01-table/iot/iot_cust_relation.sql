-- ------------------------------------
--  表名称:iot客户关联关系
--  适用数据库：MySql
--  表名称：iot_cust_relation
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.01.07
-- ------------------------------------

DROP TABLE IF EXISTS iot_cust_relation;
CREATE TABLE iot_cust_relation (
  cust_id                      varchar(32)   NOT NULL            comment '客户id',
  parent_cust_id               varchar(32)   NOT NULL            comment '上级客户编号',
  cust_leave                   int           DEFAULT NULL        comment '层级：0-父级;1-一级;2-二级;',
  app_id                       varchar(32)   DEFAULT NULL        comment '应用id',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除；',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (cust_id)
) comment 'iot客户关联关系';
