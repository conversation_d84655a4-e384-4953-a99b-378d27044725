
-- 生成数据字典类型

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('设备类型', 'iot_device_device_type', '0', 'admin', now(),'iot设备表-设备类型');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('设备状态', 'iot_device_status', '0', 'admin', now(),'iot设备表-设备状态');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('认证类型', 'iot_device_auth_type', '0', 'admin', now(),'iot设备表-认证类型');



-- 生成数据字典值

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_device_type', '0', '直连设备', 1, '', '', 'Y', '0', 'admin', now(), 'iot设备表-设备类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_device_type', '1', '网关子设备', 2, '', '', 'N', '0', 'admin', now(), 'iot设备表-设备类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_device_type', '2', '网关设备', 3, '', '', 'N', '0', 'admin', now(), 'iot设备表-设备类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_status', '0', '未激活', 1, '', '', 'Y', '0', 'admin', now(), 'iot设备表-设备状态');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_status', '1', '在线', 2, '', '', 'N', '0', 'admin', now(), 'iot设备表-设备状态');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_status', '2', '离线', 3, '', '', 'N', '0', 'admin', now(), 'iot设备表-设备状态');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_status', '3', '已禁用', 4, '', '', 'N', '0', 'admin', now(), 'iot设备表-设备状态');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_auth_type', '1', '一机一密', 1, '', '', 'Y', '0', 'admin', now(), 'iot设备表-认证类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_auth_type', '2', '动态注册', 2, '', '', 'N', '0', 'admin', now(), 'iot设备表-认证类型');



-- 生成数据字典类型

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('产品状态', 'iot_product_status', '0', 'admin', now(),'iot产品表-产品状态');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('设备类型', 'iot_product_device_type', '0', 'admin', now(),'iot产品表-设备类型');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('联网方式', 'iot_product_net_type', '0', 'admin', now(),'iot产品表-联网方式');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('接入网关协议', 'iot_product_protocol_type', '0', 'admin', now(),'iot产品表-接入网关协议');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('数据格式', 'iot_product_data_format', '0', 'admin', now(),'iot产品表-数据格式');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('数据校验级别', 'iot_product_validate_type', '0', 'admin', now(),'iot产品表-数据校验级别');



-- 生成数据字典值

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_status', '0', '开发中', 1, '', '', 'Y', '0', 'admin', now(), 'iot产品表-产品状态');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_status', '1', '已发布', 2, '', '', 'N', '0', 'admin', now(), 'iot产品表-产品状态');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_device_type', '0', '直连设备', 1, '', '', 'Y', '0', 'admin', now(), 'iot产品表-设备类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_device_type', '1', '网关子设备', 2, '', '', 'N', '0', 'admin', now(), 'iot产品表-设备类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_device_type', '2', '网关设备', 3, '', '', 'N', '0', 'admin', now(), 'iot产品表-设备类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_net_type', '0', 'WiFi', 1, '', '', 'Y', '0', 'admin', now(), 'iot产品表-联网方式');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_net_type', '1', '蜂窝（2G/3G/4G/5G）', 2, '', '', 'N', '0', 'admin', now(), 'iot产品表-联网方式');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_net_type', '2', 'Ethernet', 3, '', '', 'N', '0', 'admin', now(), 'iot产品表-联网方式');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_net_type', '3', '其他', 4, '', '', 'N', '0', 'admin', now(), 'iot产品表-联网方式');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_protocol_type', '0', '自定义', 1, '', '', 'Y', '0', 'admin', now(), 'iot产品表-接入网关协议');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_protocol_type', '1', 'Modbus', 2, '', '', 'N', '0', 'admin', now(), 'iot产品表-接入网关协议');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_protocol_type', '2', 'OPC UA', 3, '', '', 'N', '0', 'admin', now(), 'iot产品表-接入网关协议');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_protocol_type', '3', 'ZigBee', 4, '', '', 'N', '0', 'admin', now(), 'iot产品表-接入网关协议');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_protocol_type', '4', 'BLE', 5, '', '', 'N', '0', 'admin', now(), 'iot产品表-接入网关协议');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_data_format', '0', '标准数据格式（JSON）', 1, '', '', 'Y', '0', 'admin', now(), 'iot产品表-数据格式');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_data_format', '1', '透传/自定义', 2, '', '', 'N', '0', 'admin', now(), 'iot产品表-数据格式');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_validate_type', '0', '弱校验', 1, '', '', 'Y', '0', 'admin', now(), 'iot产品表-数据校验级别');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_product_validate_type', '1', '免校验', 2, '', '', 'N', '0', 'admin', now(), 'iot产品表-数据校验级别');




-- 生成数据字典类型

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('功能类型', 'iot_think_model_function_type', '0', 'admin', now(),'iot产品物模型功能-功能类型');



-- 生成数据字典值

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_think_model_function_type', '0', '属性', 1, '', '', 'Y', '0', 'admin', now(), 'iot产品物模型功能-功能类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_think_model_function_type', '1', '服务', 2, '', '', 'N', '0', 'admin', now(), 'iot产品物模型功能-功能类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_think_model_function_type', '2', '事件', 3, '', '', 'N', '0', 'admin', now(), 'iot产品物模型功能-功能类型');


-- 生成数据字典类型

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('事件类型', 'iot_device_event_event_type', '0', 'admin', now(),'iot设备事件表-事件类型');

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('是否推送', 'iot_device_event_event_is_push', '0', 'admin', now(),'iot设备事件表-是否推送');



-- 生成数据字典值

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_event_event_type', '0', '设备状态上报', 1, '', '', 'Y', '0', 'admin', now(), 'iot设备事件表-事件类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_event_event_type', '1', '注册', 2, '', '', 'N', '0', 'admin', now(), 'iot设备事件表-事件类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_event_event_type', '2', '离线', 3, '', '', 'N', '0', 'admin', now(), 'iot设备事件表-事件类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_event_event_type', '3', '上线', 4, '', '', 'N', '0', 'admin', now(), 'iot设备事件表-事件类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_event_event_type', '4', '缺水报警', 5, '', '', 'N', '0', 'admin', now(), 'iot设备事件表-事件类型');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_event_event_is_push', '0', '不推送', 1, '', '', 'Y', '0', 'admin', now(), 'iot设备事件表-是否推送');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_device_event_event_is_push', '1', '推送', 2, '', '', 'N', '0', 'admin', now(), 'iot设备事件表-是否推送');



-- 生成数据字典类型

INSERT INTO sys_dict_type ( dict_name, dict_type, status, create_by, create_time,remark)
VALUES ('层级', 'iot_cust_relation_cust_leave', '0', 'admin', now(),'iot客户关联关系-层级');



-- 生成数据字典值

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_cust_relation_cust_leave', '0', '父级', 1, '', '', 'Y', '0', 'admin', now(), 'iot客户关联关系-层级');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_cust_relation_cust_leave', '1', '一级', 2, '', '', 'N', '0', 'admin', now(), 'iot客户关联关系-层级');

INSERT INTO sys_dict_data (dict_type, dict_value, dict_label, dict_sort, css_class, list_class, is_default, `status`, create_by, create_time, remark)
VALUES ('iot_cust_relation_cust_leave', '2', '二级', 3, '', '', 'N', '0', 'admin', now(), 'iot客户关联关系-层级');