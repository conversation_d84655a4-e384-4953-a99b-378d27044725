-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备管理', '1158', '1', 'device', 'iot/device/list', 3, 0, 'C', '0', '0', 'iot:device:list', '#', 'admin', sysdate(), '', null, 'iot设备管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备管理查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'iot:device:query',        '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备管理新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'iot:device:add',          '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备管理修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'iot:device:edit',         '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备管理删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'iot:device:remove',       '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备管理导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'iot:device:export',       '#', 'admin', sysdate(), '', null, '');



-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品管理', '1158', '1', 'product', 'iot/product/list', 1, 0, 'C', '0', '0', 'iot:product:list', '#', 'admin', sysdate(), '', null, 'iot产品管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品管理查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'iot:product:query',        '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品管理新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'iot:product:add',          '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品管理修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'iot:product:edit',         '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品管理删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'iot:product:remove',       '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品管理导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'iot:product:export',       '#', 'admin', sysdate(), '', null, '');



---- 菜单 SQL
--insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
--values('物联模型管理', '1158', '1', 'thinkModelFunction', 'iot/thinkModelFunction/list', 2, 0, 'C', '0', '0', 'iot:thinkModelFunction:list', '#', 'admin', sysdate(), '', null, '物联模型管理菜单');
--
---- 按钮父菜单ID
--SELECT @parentId := LAST_INSERT_ID();
--
---- 按钮 SQL
--insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
--values('物联模型管理查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'iot:thinkModelFunction:query',        '#', 'admin', sysdate(), '', null, '');
--insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
--values('物联模型管理新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'iot:thinkModelFunction:add',          '#', 'admin', sysdate(), '', null, '');
--insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
--values('物联模型管理修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'iot:thinkModelFunction:edit',         '#', 'admin', sysdate(), '', null, '');
--insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
--values('物联模型管理删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'iot:thinkModelFunction:remove',       '#', 'admin', sysdate(), '', null, '');
--insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
--values('物联模型管理导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'iot:thinkModelFunction:export',       '#', 'admin', sysdate(), '', null, '');



-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备事件管理', '1158', '3', 'deviceEvent', 'iot/deviceEvent/list', 1, 0, 'C', '0', '0', 'iot:deviceEvent:list', '#', 'admin', sysdate(), '', null, 'iot设备事件表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备事件表查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'iot:deviceEvent:query',        '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备事件表新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'iot:deviceEvent:add',          '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备事件表修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'iot:deviceEvent:edit',         '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备事件表删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'iot:deviceEvent:remove',       '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备事件表导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'iot:deviceEvent:export',       '#', 'admin', sysdate(), '', null, '');



-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('客户关联关系', '1158', '1', 'custRelation', 'iot/custRelation/list', 1, 0, 'C', '0', '0', 'iot:custRelation:list', '#', 'admin', sysdate(), '', null, '客户关联关系菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('客户关联关系查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'iot:custRelation:query',        '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('客户关联关系新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'iot:custRelation:add',          '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('客户关联关系修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'iot:custRelation:edit',         '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('客户关联关系删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'iot:custRelation:remove',       '#', 'admin', sysdate(), '', null, '');
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('客户关联关系导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'iot:custRelation:export',       '#', 'admin', sysdate(), '', null, '');


-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备连接', '1158', '5', 'deviceClient', 'iot/deviceClient/list', 1, 0, 'C', '0', '0', 'iot:deviceClient:list', '#', 'admin', sysdate(), '', null, '设备连接菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('设备连接查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'iot:deviceClient:query',        '#', 'admin', sysdate(), '', null, '');
