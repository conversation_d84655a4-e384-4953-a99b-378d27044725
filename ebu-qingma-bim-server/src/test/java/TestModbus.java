import com.serotonin.modbus4j.BatchResults;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.code.DataType;
import com.serotonin.modbus4j.code.RegisterRange;
import com.serotonin.modbus4j.exception.ErrorResponseException;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import com.tianmasys.ebu.qingma.utils.Modbus4jUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;

@Slf4j
public class TestModbus {

    ////测试模拟
      static String host = "************";
      static int port = 502;
//     //网关设备穿透
//    static String host = "************";
//    static int port = 23;
//    //plc 主控器通信
//    static String host = "************";
//    static int port = 503;

//    static int slaveId = 1;
    static int slaveId = 1;

    static List<Integer> offsetList = new LinkedList<>();

    static int count = 5;
    static {
        offsetList.add(10);
        offsetList.add(11);
        offsetList.add(12);
        offsetList.add(13);
        offsetList.add(14);
    }

    public static void main(String[] args) throws ModbusInitException, ModbusTransportException, ErrorResponseException {

        ModbusMaster master = Modbus4jUtils.getMaster(host, port, true);

        Number number = Modbus4jUtils.readHoldingRegister(master, slaveId, 10, DataType.TWO_BYTE_INT_SIGNED);
        log.info("offset: {} ,number: {}", 10, number);


//        BatchResults<Number> numberBatchResults = Modbus4jUtils.batchReadHoldingRegister(master, slaveId,offsetList , DataType.TWO_BYTE_INT_SIGNED);
//        for (int i = 0; i < offsetList.size(); i++) {
//            log.info("offset: {} ,number: {}", offsetList.get(i), numberBatchResults.getValue(i));
//        }
//
//        Number number = Modbus4jUtils.readHoldingRegister(master, slaveId,10 , DataType.TWO_BYTE_INT_SIGNED);
//        log.info("number = {}" , number);

//        //读取网关透传值
//        for(int i=0;i<50;i++){
//            Number number = Modbus4jUtils.readInputRegisters(master, slaveId,0 , DataType.TWO_BYTE_INT_SIGNED);
//            log.info("number = {}" , number);
//        }


//        // plc主控器通信-写入和读取
//        Modbus4jUtils.writeHoldingRegister(master, slaveId, 20, 155, DataType.TWO_BYTE_INT_SIGNED);
//
//        for(int i=0; i< 200;i++){
//            Number number = Modbus4jUtils.readRegister(master, slaveId, 0+i, DataType.TWO_BYTE_INT_SIGNED, RegisterRange.HOLDING_REGISTER);
//            log.info("offset: {} ,number: {}", i, number);
//        }




    }
}
