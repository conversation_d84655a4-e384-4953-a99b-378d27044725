package com.tianmasys.ebu.qingma.config.feign;

import com.tianmasys.ebu.qingma.config.properties.FeignRouteConfigProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignClientConfig {


    @Autowired
    private FeignRouteConfigProperties feignRouteConfigProperties;

    private String ebuSystemUrl;


    @Bean
    public DynamicUrlInterceptor dynamicUrlInterceptor() {
        DynamicUrlInterceptor dynamicUrlInterceptor = new DynamicUrlInterceptor();

//        Map<String, FeignRouteConfig> collect = feignRouteConfigProperties.getFeignRoutes().stream().collect(Collectors.toMap(FeignRouteConfig::getId, item-> item));
        dynamicUrlInterceptor.setFeignRouteConfigMap(feignRouteConfigProperties.getFeignRoutes());

        return dynamicUrlInterceptor;
    }


//    @Bean
//    public FeignClientBuilder feignClientBuilder() {
////        return Feign.builder()
////                .requestInterceptor(template -> {
////                    if (StringUtils.hasText(ebuSystemUrl)) {
////                        template.target(ebuSystemUrl);
////                    }
////                });
////        FeignClientBuilder feignClientBuilder = new FeignClientBuilder();
//
//    }
}