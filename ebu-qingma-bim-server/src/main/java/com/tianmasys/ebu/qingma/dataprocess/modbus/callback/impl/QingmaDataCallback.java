package com.tianmasys.ebu.qingma.dataprocess.modbus.callback.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.tianmasys.ebu.qingma.dataprocess.modbus.callback.ModbusDataCallback;
import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.MasterData;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.ModbusConfig;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.SlaveData;
import com.tianmasys.ebu.qingma.dataprocess.modbus.storage.ModbusDataStorage;
import com.tianmasys.ebu.qingma.utils.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 青马大桥数据处理回调
 */
@Slf4j
@Component
public class QingmaDataCallback implements ModbusDataCallback {


    @Resource
    private ModbusConfig modbusConfig;

    @Resource
    private ModbusDataStorage modbusDataStorage;
    
    // 用于存储上一次的value值，key为"siteId_masterId_slaveId_dataKey"
    private final Map<String, Object> previousValues = new ConcurrentHashMap<>();

    @Override
    public void onDataReceived(MasterData masterData, SlaveData slaveData, Map<String, Object> data) {
        Integer siteId = masterData.getSiteId();
        String masterDataId = masterData.getId();
        Integer slaveId = slaveData.getSlaveId();

        Map<String, Object> dealData = new LinkedHashMap<>();

        JSONArray jsonArray = JSONUtil.parseArray(slaveData.getMappingConfig());
        jsonArray.forEach(slave -> {
            String resKey = MessageUtils.parseMessage((Map<String, Object>)data.get("data"), slave.toString());
            dealData.putAll(BeanUtil.beanToMap(JSONUtil.parseObj(resKey)));
        });
        
        // 处理trend字段，根据上一次的value值判断是上升还是下降
        for (Map.Entry<String, Object> entry : dealData.entrySet()) {
            String dataKey = entry.getKey();
            Object dataValue = entry.getValue();
            
            if (dataValue instanceof Map) {
                Map<String, Object> valueMap = (Map<String, Object>) dataValue;
                Object currentValueObj = valueMap.get("value");
                
                // 构造唯一标识符
                String uniqueKey = String.format("%s_%s_%s_%s", siteId,masterDataId, slaveId, dataKey);
                
                // 获取上一次的值
                Object previousValue = previousValues.get(uniqueKey);
                
                // 如果有上一次的值，则判断趋势
                if (previousValue != null && currentValueObj != null) {
                    try {
                        double currentValue = Double.parseDouble(currentValueObj.toString());
                        double lastValue = Double.parseDouble(previousValue.toString());
                        
                        if (currentValue > lastValue) {
                            valueMap.put("trend", "up");
                        } else if (currentValue < lastValue) {
                            valueMap.put("trend", "down");
                        } else {
                            valueMap.put("trend", "stable");
                        }
                    } catch (NumberFormatException e) {
                        // 如果无法转换为数字，则保持原样
                        valueMap.put("trend", "unknown");
                    }
                } else {
                    valueMap.put("trend", "unknown");
                }
                
                // 存储当前值作为下一次比较的依据
                if (currentValueObj != null) {
                    previousValues.put(uniqueKey, currentValueObj);
                }
            }
        }
        
        data.put("data", dealData);

        String key = String.format("%s_%s_%s",siteId, masterDataId, slaveId);

//        data = dealData(slaveData,data);

//        log.debug("主站{}，从站{}，处理完成后数据：{}", masterData.getId(),slaveId,data);
        // 将处理后的数据存储到共享存储中
        modbusDataStorage.updateData(key, data);
        // 可以在这里实现具体的业务逻辑
        // 例如：保存到数据库、发送到消息队列、触发其他业务等
    }
}