package com.tianmasys.ebu.qingma.dataprocess.modbus.service;

import com.tianmasys.ebu.qingma.dataprocess.modbus.callback.ModbusDataCallback;
import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.SlaveData;

import java.util.Map;

/**
 * 用于处理MQTT消息的具体业务逻辑，如订阅回调
 *
 * <AUTHOR>
 */
public interface ModbusService {


    /**
     *  读取modbus数据
     * @param client modbus 客户端
     * @param slaveData modbus 从站数据
     */
    Map<String, Object> readData(ModbusClient client, SlaveData slaveData);

    /**
     *
     *
     * @param slaveId  从站id
     * @param datas 读取到的数据
     */
    void readDataCallback(String slaveId, Map<String,Object> datas);



}
