package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * iot产品表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */
@Data
@Schema(description = "iot产品表")
public class ProductVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "产品ID")
	private Long id;
	
	@Schema(description = "产品名称")
	@NotEmpty(message = "产品名称不能为空!")
	private String name;
	
	@Schema(description = "产品标识")
	private String productKey;
	
	@Schema(description = "产品所属品类编号")
	private String categoryId;
	
	@Schema(description = "产品描述")
	@NotEmpty(message = "产品描述不能为空!")
	private String description;
	
	@Schema(description = "产品状态:0-开发中;1-已发布;")
	@NotEmpty(message = "产品状态不能为空!")
	private String status;
	
	@Schema(description = "设备类型:0-直连设备;1-网关子设备;2-网关设备;")
	@NotEmpty(message = "设备类型不能为空!")
	private String deviceType;
	
	@Schema(description = "联网方式:0-WiFi;1-Cellular;2-Ethernet;3-其他;")
	@NotEmpty(message = "联网方式不能为空!")
	private String netType;
	
	@Schema(description = "接入网关协议:0-自定义;1-Modbus;2-OPC UA;3-ZigBee;4-BLE;")
	@NotEmpty(message = "接入网关协议不能为空!")
	private String protocolType;
	
	@Schema(description = "协议编号")
	private Long protocolId;
	
	@Schema(description = "数据格式:0-标准数据格式（JSON）;1-透传/自定义;")
	@NotEmpty(message = "数据格式不能为空!")
	private String dataFormat;
	
	@Schema(description = "数据校验级别:0-弱校验;1-免校验")
	@NotEmpty(message = "数据校验级别不能为空!")
	private String validateType;
	
	@Schema(description = "是否删除：0-正常；1-删除；")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;
	



}