package com.tianmasys.ebu.qingma.dataprocess.modbus.client;

import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.qingma.dataprocess.modbus.callback.ModbusDataCallback;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.MasterData;
import com.tianmasys.ebu.qingma.utils.Modbus4jUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * modbus客户端类,master模式，从“slave”读取数据
 *
 * <AUTHOR>
 */
@Slf4j
@Data
//@Component
//@ConditionalOnBean(ModbusConfig.class)
public class ModbusClient {

    // 存储slaveId与回调函数的映射关系
    private final Map<Integer, ModbusDataCallback> callbackMap = new ConcurrentHashMap<>();

    private ModbusMaster  master;

    private MasterData masterData;

    public ModbusClient(MasterData masterData) {
        this.masterData = masterData;
    }



    // 示例回调函数
//    private QingmaDataCallback exampleDataCallback;

    public void connect(ModbusDataCallback modbusDataCallback)  {
        try {
            if(master == null){
                if(StringUtils.isBlank(masterData.getHost())){
                    log.error("Modbus4j初始化失败，请检查配置文件");
                    return;
                }
                master = Modbus4jUtils.getMaster(masterData.getHost(), masterData.getPort(), masterData.getKeepAlive());

                //注册回调
                masterData.getSlaves().forEach(slaveData -> {
                    registerCallback(slaveData.getSlaveId(), modbusDataCallback);
                    log.debug("成功为siteId={},masterId={},slaveId={}注册回调函数",masterData.getSiteId(),masterData.getId(), slaveData.getSlaveId());
                });
            }
            log.info("Modbus4j初始化成功");

        } catch (ModbusInitException e) {
            log.error("Modbus4j初始化失败", e);
        }
    }

    public ModbusMaster getMaster() {
        return master;
    }



    /**
     * 为指定的slaveId注册回调函数
     * @param slaveId 从站ID
     * @param callback 回调函数
     */
    public void registerCallback(Integer slaveId, ModbusDataCallback callback) {
        if (slaveId == null || callback == null) {
            log.warn("注册回调函数失败，slaveId或callback为空");
            return;
        }
        callbackMap.put(slaveId, callback);

    }

    /**
     * 移除指定slaveId的回调函数
     * @param slaveId 从站ID
     */
    public void unregisterCallback(Integer slaveId) {
        if (slaveId == null) {
            log.warn("移除回调函数失败，slaveId为空");
            return;
        }
        ModbusDataCallback removed = callbackMap.remove(slaveId);
        if (removed != null) {
            log.debug("成功移除slaveId={}的回调函数", slaveId);
        } else {
            log.debug("未找到slaveId={}对应的回调函数", slaveId);
        }
    }

    /**
     * 获取指定slaveId的回调函数
     * @param slaveId 从站ID
     * @return 回调函数，如果不存在则返回null
     */
    public ModbusDataCallback getCallback(Integer slaveId) {
        return callbackMap.get(slaveId);
    }

    /**
     * 获取所有回调函数映射
     * @return 回调函数映射
     */
    public Map<Integer, ModbusDataCallback> getCallbacks() {
        return callbackMap;
    }




}