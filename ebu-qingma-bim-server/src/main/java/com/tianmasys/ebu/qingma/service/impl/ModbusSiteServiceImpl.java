package com.tianmasys.ebu.qingma.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tianmasys.ebu.qingma.domain.ModbusConfigData;
import com.tianmasys.ebu.qingma.domain.ModbusPointsEntity;
import com.tianmasys.ebu.qingma.mapper.ModbusPointsMapper;
import com.tianmasys.ebu.qingma.service.IModbusPointsService;
import com.tianmasys.ebu.qingma.vo.ModbusPointsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ModbusSiteMapper;
import com.tianmasys.ebu.qingma.service.IModbusSiteService;
import com.tianmasys.ebu.qingma.vo.ModbusSiteVO;
import com.tianmasys.ebu.qingma.domain.ModbusSiteEntity;

/**
 * modbus站点Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Service
public class ModbusSiteServiceImpl extends ServiceImpl<ModbusSiteMapper,ModbusSiteEntity> implements IModbusSiteService 
{
    @Autowired
    private ModbusSiteMapper modbusSiteMapper;

    @Autowired
    private ModbusPointsMapper modbusPointsMapper;

    /**
     * 查询modbus站点详情
     * 
     * @param id modbus站点主键
     * @return modbus站点
     */
    @Override
    public ModbusSiteVO selectModbusSiteById(Long id)
    {
        ModbusSiteEntity entity = modbusSiteMapper.selectById(id);
        return BeanUtil.toBean(entity,ModbusSiteVO.class);
    }

    /**
     * 查询modbus站点列表
     * 
     * @param modbusSiteVO modbus站点
     * @return modbus站点
     */
    @Override
    public List<ModbusSiteVO> selectModbusSiteList(ModbusSiteVO modbusSiteVO)
    {
        List<ModbusSiteEntity> modbusSiteEntities = modbusSiteMapper.selectList(getWrapper(modbusSiteVO));
        return PageObjectConvertUtil.convert(modbusSiteEntities,ModbusSiteVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ModbusSiteEntity> getWrapper(ModbusSiteVO query){
        LambdaQueryWrapper<ModbusSiteEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getSiteId()), ModbusSiteEntity::getSiteId, query.getSiteId());
        wrapper.like(ObjUtil.isNotEmpty(query.getSiteName()), ModbusSiteEntity::getSiteName, query.getSiteName());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDescription()), ModbusSiteEntity::getDescription, query.getDescription());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMasterId()), ModbusSiteEntity::getMasterId, query.getMasterId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getHost()), ModbusSiteEntity::getHost, query.getHost());
        wrapper.eq(ObjUtil.isNotEmpty(query.getPort()), ModbusSiteEntity::getPort, query.getPort());
        wrapper.eq(ObjUtil.isNotEmpty(query.getKeepAlive()), ModbusSiteEntity::getKeepAlive, query.getKeepAlive());
        return wrapper;
    }

    /**
     * 新增modbus站点
     * 
     * @param modbusSiteVO modbus站点
     * @return 结果
     */
    @Override
    public int insertModbusSite(ModbusSiteVO modbusSiteVO)
    {
       ModbusSiteEntity entity = BeanUtil.toBean(modbusSiteVO, ModbusSiteEntity.class);
       return modbusSiteMapper.insert(entity);
    }

    /**
     * 修改modbus站点
     * 
     * @param modbusSiteVO modbus站点
     * @return 结果
     */
    @Override
    public int updateModbusSite(ModbusSiteVO modbusSiteVO)
    {
       ModbusSiteEntity entity = BeanUtil.toBean(modbusSiteVO, ModbusSiteEntity.class);
        return modbusSiteMapper.updateById(entity);
    }

    /**
     * 批量删除modbus站点
     * 
     * @param ids 需要删除的modbus站点主键
     * @return 结果
     */
    @Override
    public int deleteModbusSiteByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return modbusSiteMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除modbus站点信息
     * 
     * @param id modbus站点主键
     * @return 结果
     */
    @Override
    public int deleteModbusSiteById(Long id)
    {

        return modbusSiteMapper.deleteById(id);
    }

    /**
     * 获取modbus站点列表相关配置
     *
     * @return
     */
    @Override
    public List<ModbusConfigData> getModbusConfigDataList() {

        List<ModbusConfigData> resList = new ArrayList<>();

        List<ModbusSiteEntity> modbusSiteEntities = modbusSiteMapper.selectList(Wrappers.lambdaQuery());

        List<ModbusPointsEntity> modbusPointsEntities = modbusPointsMapper.selectList(
                Wrappers.lambdaQuery(ModbusPointsEntity.class).eq(ModbusPointsEntity::getStatus, 1));


        Map<Long, List<ModbusPointsEntity>> pointsGroupedByMasterId = modbusPointsEntities.stream()
                .collect(Collectors.groupingBy(ModbusPointsEntity::getMasterId));


        modbusSiteEntities.stream().forEach(item->{
            ModbusConfigData modbusConfigData = BeanUtil.toBean(item,ModbusConfigData.class);
            modbusConfigData.setPoints(pointsGroupedByMasterId.get(item.getMasterId()));
            resList.add(modbusConfigData);
        });



        return resList;
    }

}
