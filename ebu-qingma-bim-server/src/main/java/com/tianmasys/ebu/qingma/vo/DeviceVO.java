package com.tianmasys.ebu.qingma.vo;

import com.tianmasys.ebu.qingma.domain.DeviceExtendConfig;
import com.tianmasys.ebu.qingma.domain.DeviceTaskConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * iot设备表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */
@Data
@Schema(description = "iot设备表")
public class DeviceVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "设备 ID")
	private Long id;
	
	@Schema(description = "设备唯一标识符(全局唯一，用于识别设备)")
	private String deviceKey;
	
	@Schema(description = "设备名称(在产品内唯一，用于标识设备)")
	@NotEmpty(message = "设备名称不能为空!")
	private String deviceName;
	
	@Schema(description = "设备备注名称")
	@NotEmpty(message = "设备备注名称不能为空!")
	private String nickname;
	
	@Schema(description = "设备序列号")
	private String serialNumber;
	
	@Schema(description = "产品编号")
	@NotNull(message = "产品编号不能为空!")
	private Long productId;
	
	@Schema(description = "产品标识")
	private String productKey;
	
	@Schema(description = "设备类型:0-直连设备;1-网关子设备;2-网关设备;")
	private String deviceType;
	
	@Schema(description = "设备状态:0-未激活;1-在线;2-离线;3-已禁用;")
	private String status;
	
	@Schema(description = "网关设备编号(子设备需要关联的网关设备 ID)")
	private Long gatewayId;
	
	@Schema(description = "设备状态最后更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date statusLastUpdateTime;
	
	@Schema(description = "最后上线时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date lastOnlineTime;
	
	@Schema(description = "最后离线时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date lastOfflineTime;
	
	@Schema(description = "设备激活时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date activeTime;
	
	@Schema(description = "设备的IP地址")
	private String ip;
	
	@Schema(description = "设备的固件版本")
	private String firmwareVersion;
	
	@Schema(description = "设备密钥(用于设备认证,需安全存储)")
	private String deviceSecret;
	
	@Schema(description = "MQTT客户端ID")
	private String mqttClientId;
	
	@Schema(description = "MQTT用户名")
	private String mqttUsername;
	
	@Schema(description = "MQTT密码")
	private String mqttPassword;
	
	@Schema(description = "认证类型:1-一机一密;2-动态注册")
	@NotEmpty(message = "认证类型不能为空!")
	private String authType;
	
	@Schema(description = "设备位置的纬度")
	private String latitude;
	
	@Schema(description = "设备位置的经度")
	private String longitude;
	
	@Schema(description = "地区编码(关联Area的id)")
	private List<String> areaId;
	
	@Schema(description = "设备详细地址")
	private String address;
	
	@Schema(description = "是否删除：0-正常；1-删除；")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;
	

	@Schema(description = "设备状态最后更新时间-开始")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date beginStatusLastUpdateTime;
	
	@Schema(description = "设备状态最后更新时间-结束")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endStatusLastUpdateTime;
	
	@Schema(description = "最后上线时间-开始")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date beginLastOnlineTime;
	
	@Schema(description = "最后上线时间-结束")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endLastOnlineTime;

	@Schema(description = "信号强度")
	private String csq;

	@Schema(description = "运行状态")
	private String pinState;

	@Schema(description = "水位状态")
	private String waterLevel;

	@Schema(description = "最后更新时间")
	private String lastUpdateTime;

	@Schema(description = "产品名称")
	private String productName;

	@Schema(description = "扩展配置")
	private DeviceExtendConfig extendConfig;

	@Schema(description = "任务配置")
	private List<DeviceTaskConfig> taskConfigList;

	@Schema(description = "客户编号")
	private String custId;

	@Schema(description = "用户名")
	private String userName;

	@Schema(description = "搜索内容")
	private String searchValue;


	@Schema(description = "设备排序")
	private String deviceSort;
}