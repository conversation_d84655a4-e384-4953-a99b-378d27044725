package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * iot产品物模型功能
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */
@Data
@Schema(description = "iot产品物模型功能")
public class ThinkModelFunctionVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "设备 ID")
	private Long id;
	
	@Schema(description = "设备唯一标识符(全局唯一，用于识别设备)")
	@NotEmpty(message = "设备唯一标识符不能为空!")
	private String identifier;
	
	@Schema(description = "功能名称")
	@NotEmpty(message = "功能名称不能为空!")
	private String name;
	
	@Schema(description = "功能描述")
	@NotEmpty(message = "功能描述不能为空!")
	private String description;
	
	@Schema(description = "产品id")
	@NotEmpty(message = "产品id不能为空!")
	private Long productId;
	
	@Schema(description = "产品标识")
	@NotEmpty(message = "产品标识不能为空!")
	private String productKey;
	
	@Schema(description = "功能类型:0-属性;1-服务;2-事件;")
	@NotEmpty(message = "功能类型不能为空!")
	private String type;
	
	@Schema(description = "属性")
	@NotEmpty(message = "属性不能为空!")
	private String property;
	
	@Schema(description = "事件")
	@NotEmpty(message = "事件不能为空!")
	private String event;
	
	@Schema(description = "服务")
	@NotEmpty(message = "服务不能为空!")
	private String service;
	
	@Schema(description = "是否删除：0-正常；1-删除；")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;
	



}