package com.tianmasys.ebu.qingma.dataprocess.modbus.service;

import cn.hutool.core.date.DateUtil;
import com.serotonin.modbus4j.BatchResults;
import com.serotonin.modbus4j.code.DataType;
import com.serotonin.modbus4j.exception.ErrorResponseException;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import com.tianmasys.ebu.common.core.utils.DateUtils;
import com.tianmasys.ebu.qingma.dataprocess.modbus.callback.ModbusDataCallback;
import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.SlaveData;
import com.tianmasys.ebu.qingma.utils.Modbus4jUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * 用于处理MQTT消息的具体业务逻辑，如订阅回调
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ModbusServiceImpl implements ModbusService {
    /**
     * 读取modbus数据
     *
     * @param client    modbus 客户端
     * @param slaveData modbus 从站数据
     */
    @Override
    public Map<String, Object> readData(ModbusClient client, SlaveData slaveData) {
        Map<String, Object> res =  new LinkedHashMap<>();
        Long startTime = System.currentTimeMillis();


        try {
            List<Integer> offsetList = slaveData.getOffsetList();
//            BatchResults<Number> numberBatchResults = Modbus4jUtils.batchReadHoldingRegister(client.getMaster(), slaveData.getSlaveId(), offsetList, DataType.FOUR_BYTE_INT_UNSIGNED);
            BatchResults<Number> numberBatchResults = Modbus4jUtils.batchReadHoldingRegister(client.getMaster(), slaveData.getSlaveId(), offsetList, DataType.TWO_BYTE_INT_UNSIGNED);
//            log.info("从站id: {}, 批量读取数据: {}", slaveData.getSlaveId(), numberBatchResults);
            Map<String, Object> data =  new LinkedHashMap<>();
            for(int i =0 ;i<offsetList.size();i++){
                data.put("address_"+String.valueOf(offsetList.get(i)), numberBatchResults.getValue(i));
            }
            res.put("data", data);
            res.put("status", "success");
            res.put("slaveId",slaveData.getSlaveId());
            Long finshTime = System.currentTimeMillis();
            res.put("startTime", startTime);
            res.put("fnishTime", finshTime);
            res.put("time", finshTime- startTime);
            return res;
        } catch (ModbusInitException e) {
            throw new RuntimeException(e);
        } catch (ModbusTransportException e) {
            throw new RuntimeException(e);
        } catch (ErrorResponseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param slaveId 从站id
     * @param datas   读取到的数据
     */
    @Override
    public void readDataCallback(String slaveId, Map<String, Object> datas) {

    }



}
