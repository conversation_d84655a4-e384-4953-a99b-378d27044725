package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * modbus点位
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */
@Data
@Schema(description = "modbus点位")
public class ModbusPointsVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "点位 ID")
	private Long id;
	
	@Schema(description = "点位名称")
	@NotEmpty(message = "点位名称不能为空!")
	private String pointName;
	
	@Schema(description = "点位描述")
	@NotEmpty(message = "点位描述不能为空!")
	private String description;
	
	@Schema(description = "所属站点ID")
	@NotEmpty(message = "所属站点ID不能为空!")
	private Long siteId;
	
	@Schema(description = "主站ID")
	@NotEmpty(message = "主站ID不能为空!")
	private Long masterId;
	
	@Schema(description = "从站ID")
	@NotEmpty(message = "从站ID不能为空!")
	private Long slaveId;
	
	@Schema(description = "功能码：1-COIL_STATUS;2-INPUT_STATUS;3-HOLDING_REGISTER;4-INPUT_REGISTER")
	@NotEmpty(message = "功能码不能为空!")
	private String functionCode;
	
	@Schema(description = "寄存器地址")
	@NotEmpty(message = "寄存器地址不能为空!")
	private Integer registerAddress;
	
	@Schema(description = "状态：0-禁用；1-启用;")
	@NotEmpty(message = "状态不能为空!")
	private Integer status;
	
	@Schema(description = "自动读取：0-不读取；1-自动读取;")
	@NotEmpty(message = "自动读取不能为空!")
	private Integer autoRead;
	
	@Schema(description = "采集间隔，单位毫秒")
	@NotEmpty(message = "采集间隔，单位毫秒不能为空!")
	private Integer interval;
	
	@Schema(description = "数据类型:2-TWO_BYTE_INT_UNSIGNED;3-TWO_BYTE_INT_SIGNED;")
	@NotEmpty(message = "数据类型不能为空!")
	private String dataType;
	
	@Schema(description = "映射键名称，json。支持对象和path")
	@NotEmpty(message = "映射键名称，json。支持对象和path不能为空!")
	private String mappingConfig;
	
	@Schema(description = "是否删除：0-正常；1-删除;")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;
	



}