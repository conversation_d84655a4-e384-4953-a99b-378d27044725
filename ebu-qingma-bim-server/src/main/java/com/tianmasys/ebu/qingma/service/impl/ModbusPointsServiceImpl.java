package com.tianmasys.ebu.qingma.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ModbusPointsMapper;
import com.tianmasys.ebu.qingma.service.IModbusPointsService;
import com.tianmasys.ebu.qingma.vo.ModbusPointsVO;
import com.tianmasys.ebu.qingma.domain.ModbusPointsEntity;

/**
 * modbus点位Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Service
public class ModbusPointsServiceImpl extends ServiceImpl<ModbusPointsMapper,ModbusPointsEntity> implements IModbusPointsService 
{
    @Autowired
    private ModbusPointsMapper modbusPointsMapper;

    /**
     * 查询modbus点位详情
     * 
     * @param id modbus点位主键
     * @return modbus点位
     */
    @Override
    public ModbusPointsVO selectModbusPointsById(Long id)
    {
        ModbusPointsEntity entity = modbusPointsMapper.selectById(id);
        return BeanUtil.toBean(entity,ModbusPointsVO.class);
    }

    /**
     * 查询modbus点位列表
     * 
     * @param modbusPointsVO modbus点位
     * @return modbus点位
     */
    @Override
    public List<ModbusPointsVO> selectModbusPointsList(ModbusPointsVO modbusPointsVO)
    {
        List<ModbusPointsEntity> modbusPointsEntities = modbusPointsMapper.selectList(getWrapper(modbusPointsVO));
        return PageObjectConvertUtil.convert(modbusPointsEntities,ModbusPointsVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ModbusPointsEntity> getWrapper(ModbusPointsVO query){
        LambdaQueryWrapper<ModbusPointsEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(ObjUtil.isNotEmpty(query.getPointName()), ModbusPointsEntity::getPointName, query.getPointName());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDescription()), ModbusPointsEntity::getDescription, query.getDescription());
        wrapper.eq(ObjUtil.isNotEmpty(query.getSiteId()), ModbusPointsEntity::getSiteId, query.getSiteId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMasterId()), ModbusPointsEntity::getMasterId, query.getMasterId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getSlaveId()), ModbusPointsEntity::getSlaveId, query.getSlaveId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getFunctionCode()), ModbusPointsEntity::getFunctionCode, query.getFunctionCode());
        wrapper.eq(ObjUtil.isNotEmpty(query.getRegisterAddress()), ModbusPointsEntity::getRegisterAddress, query.getRegisterAddress());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStatus()), ModbusPointsEntity::getStatus, query.getStatus());
        wrapper.eq(ObjUtil.isNotEmpty(query.getAutoRead()), ModbusPointsEntity::getAutoRead, query.getAutoRead());
        wrapper.eq(ObjUtil.isNotEmpty(query.getInterval()), ModbusPointsEntity::getInterval, query.getInterval());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDataType()), ModbusPointsEntity::getDataType, query.getDataType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMappingConfig()), ModbusPointsEntity::getMappingConfig, query.getMappingConfig());
        return wrapper;
    }

    /**
     * 新增modbus点位
     * 
     * @param modbusPointsVO modbus点位
     * @return 结果
     */
    @Override
    public int insertModbusPoints(ModbusPointsVO modbusPointsVO)
    {
       ModbusPointsEntity entity = BeanUtil.toBean(modbusPointsVO, ModbusPointsEntity.class);
       return modbusPointsMapper.insert(entity);
    }

    /**
     * 修改modbus点位
     * 
     * @param modbusPointsVO modbus点位
     * @return 结果
     */
    @Override
    public int updateModbusPoints(ModbusPointsVO modbusPointsVO)
    {
       ModbusPointsEntity entity = BeanUtil.toBean(modbusPointsVO, ModbusPointsEntity.class);
        return modbusPointsMapper.updateById(entity);
    }

    /**
     * 批量删除modbus点位
     * 
     * @param ids 需要删除的modbus点位主键
     * @return 结果
     */
    @Override
    public int deleteModbusPointsByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return modbusPointsMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除modbus点位信息
     * 
     * @param id modbus点位主键
     * @return 结果
     */
    @Override
    public int deleteModbusPointsById(Long id)
    {

        return modbusPointsMapper.deleteById(id);
    }

}
