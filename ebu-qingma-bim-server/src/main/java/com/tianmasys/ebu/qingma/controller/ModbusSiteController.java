package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.ModbusSiteVO;
import com.tianmasys.ebu.qingma.service.IModbusSiteService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * modbus站点Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-08-06
 */
@RestController
@RequestMapping("/modbusSite")
public class ModbusSiteController extends BaseController
{
    @Autowired
    private IModbusSiteService modbusSiteService;

    /**
     * 查询modbus站点列表
     */
    @RequiresPermissions("qingma:modbusSite:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<ModbusSiteVO>> list(ModbusSiteVO modbusSiteVO)
    {
        startPage();
        List<ModbusSiteVO> list = modbusSiteService.selectModbusSiteList(modbusSiteVO);
        return success(getDataTable(list));
    }


    /**
     * 导出modbus站点列表
     */
    @RequiresPermissions("qingma:modbusSite:export")
    @Log(title = "modbus站点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModbusSiteVO modbusSiteVO)
    {
        List<ModbusSiteVO> list = modbusSiteService.selectModbusSiteList(modbusSiteVO);
        ExcelUtil<ModbusSiteVO> util = new ExcelUtil<ModbusSiteVO>(ModbusSiteVO.class);
        util.exportExcel(response, list, "modbus站点数据");
    }

    /**
     * 获取modbus站点详细信息
     */
    @RequiresPermissions("qingma:modbusSite:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<ModbusSiteVO> getInfo(@PathVariable("id") Long id)
    {
        return success(modbusSiteService.selectModbusSiteById(id));
    }

    /**
     * 新增modbus站点
     */
    @RequiresPermissions("qingma:modbusSite:add")
    @Log(title = "modbus站点", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<ModbusSiteVO> add(@Valid @RequestBody ModbusSiteVO modbusSiteVO)
    {
        return toAjax(modbusSiteService.insertModbusSite(modbusSiteVO));
    }

    /**
     * 修改modbus站点
     */
    @RequiresPermissions("qingma:modbusSite:edit")
    @Log(title = "modbus站点", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<ModbusSiteVO> edit(@Valid @RequestBody ModbusSiteVO modbusSiteVO)
    {
        return toAjax(modbusSiteService.updateModbusSite(modbusSiteVO));
    }

    /**
     * 删除modbus站点
     */
    @RequiresPermissions("qingma:modbusSite:remove")
    @Log(title = "modbus站点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<ModbusSiteVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(modbusSiteService.deleteModbusSiteByIds(ids));
    }
}
