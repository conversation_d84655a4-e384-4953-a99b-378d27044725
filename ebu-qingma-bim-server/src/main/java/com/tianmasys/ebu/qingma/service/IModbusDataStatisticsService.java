package com.tianmasys.ebu.qingma.service;

import java.util.List;
import com.tianmasys.ebu.qingma.vo.ModbusDataStatisticsVO;
import com.tianmasys.ebu.qingma.vo.HistoryDataRequestVO;
import com.tianmasys.ebu.qingma.vo.HistoryDataResponseVO;
import com.tianmasys.ebu.qingma.domain.ModbusDataStatisticsEntity;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * modbus统计数据统计表Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-07
 */
public interface IModbusDataStatisticsService extends IService<ModbusDataStatisticsEntity>
{
    /**
     * 查询modbusDataStatistics
     * 
     * @param id modbusDataStatistics主键
     * @return modbusDataStatistics
     */
    public ModbusDataStatisticsVO selectModbusDataStatisticsById(Long id);

    /**
     * 查询modbusDataStatistics列表
     * 
     * @param modbusDataStatisticsVO modbusDataStatistics
     * @return modbusDataStatistics集合
     */
    public List<ModbusDataStatisticsVO> selectModbusDataStatisticsList(ModbusDataStatisticsVO modbusDataStatisticsVO);

    /**
     * 新增modbusDataStatistics
     * 
     * @param modbusDataStatisticsVO modbusDataStatistics
     * @return 结果
     */
    public int insertModbusDataStatistics(ModbusDataStatisticsVO modbusDataStatisticsVO);

    /**
     * 修改modbusDataStatistics
     * 
     * @param modbusDataStatisticsVO modbusDataStatistics
     * @return 结果
     */
    public int updateModbusDataStatistics(ModbusDataStatisticsVO modbusDataStatisticsVO);

    /**
     * 批量删除modbusDataStatistics
     * 
     * @param ids 需要删除的modbusDataStatistics主键集合
     * @return 结果
     */
    public int deleteModbusDataStatisticsByIds(Long[] ids);

    /**
     * 删除modbusDataStatistics信息
     *
     * @param id modbusDataStatistics主键
     * @return 结果
     */
    public int deleteModbusDataStatisticsById(Long id);

    /**
     * 获取历史统计数据
     *
     * @param request 历史数据查询请求参数
     * @return 历史数据响应结果
     */
    public HistoryDataResponseVO getHistoryData(HistoryDataRequestVO request);
}
