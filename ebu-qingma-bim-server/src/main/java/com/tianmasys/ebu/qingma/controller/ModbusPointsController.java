package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.ModbusPointsVO;
import com.tianmasys.ebu.qingma.service.IModbusPointsService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * modbus点位Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-08-06
 */
@RestController
@RequestMapping("/modbusPoints")
public class ModbusPointsController extends BaseController
{
    @Autowired
    private IModbusPointsService modbusPointsService;

    /**
     * 查询modbus点位列表
     */
    @RequiresPermissions("qingma:modbusPoints:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<ModbusPointsVO>> list(ModbusPointsVO modbusPointsVO)
    {
        startPage();
        List<ModbusPointsVO> list = modbusPointsService.selectModbusPointsList(modbusPointsVO);
        return success(getDataTable(list));
    }


    /**
     * 导出modbus点位列表
     */
    @RequiresPermissions("qingma:modbusPoints:export")
    @Log(title = "modbus点位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModbusPointsVO modbusPointsVO)
    {
        List<ModbusPointsVO> list = modbusPointsService.selectModbusPointsList(modbusPointsVO);
        ExcelUtil<ModbusPointsVO> util = new ExcelUtil<ModbusPointsVO>(ModbusPointsVO.class);
        util.exportExcel(response, list, "modbus点位数据");
    }

    /**
     * 获取modbus点位详细信息
     */
    @RequiresPermissions("qingma:modbusPoints:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<ModbusPointsVO> getInfo(@PathVariable("id") Long id)
    {
        return success(modbusPointsService.selectModbusPointsById(id));
    }

    /**
     * 新增modbus点位
     */
    @RequiresPermissions("qingma:modbusPoints:add")
    @Log(title = "modbus点位", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<ModbusPointsVO> add(@Valid @RequestBody ModbusPointsVO modbusPointsVO)
    {
        return toAjax(modbusPointsService.insertModbusPoints(modbusPointsVO));
    }

    /**
     * 修改modbus点位
     */
    @RequiresPermissions("qingma:modbusPoints:edit")
    @Log(title = "modbus点位", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<ModbusPointsVO> edit(@Valid @RequestBody ModbusPointsVO modbusPointsVO)
    {
        return toAjax(modbusPointsService.updateModbusPoints(modbusPointsVO));
    }

    /**
     * 删除modbus点位
     */
    @RequiresPermissions("qingma:modbusPoints:remove")
    @Log(title = "modbus点位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<ModbusPointsVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(modbusPointsService.deleteModbusPointsByIds(ids));
    }
}
