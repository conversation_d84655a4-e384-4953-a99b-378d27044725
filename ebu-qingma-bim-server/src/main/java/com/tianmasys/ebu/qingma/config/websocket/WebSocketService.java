package com.tianmasys.ebu.qingma.config.websocket;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket服务类
 * 用于向客户端发送modbus数据
 */
@Slf4j
@Service
public class WebSocketService {

    /**
     * 存储所有活跃的WebSocket会话
     */
    private static final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    public int getSessionCount() {
        return sessions.size();
    }

    /**
     * 添加WebSocket会话
     *
     * @param session 会话
     */
    public void addSession(WebSocketSession session) {
        sessions.put(session.getId(), session);
        log.info("WebSocket连接已建立: {}", session.getId());
    }

    /**
     * 移除WebSocket会话
     *
     * @param session 会话
     */
    public void removeSession(WebSocketSession session) {
        sessions.remove(session.getId());
        log.info("WebSocket连接已关闭: {}", session.getId());
    }

    /**
     * 向所有连接的客户端广播数据
     *
     * @param data 数据
     */
    public void broadcastData(Object data) {
        if(data == null){
            return;
        }
        String jsonData = JSONUtil.parseObj(data).toString();
        TextMessage message = new TextMessage(jsonData);
        
        sessions.values().forEach(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(message);
                }
            } catch (Exception e) {
                log.error("发送WebSocket消息失败", e);
                // 如果发送失败，移除会话
                removeSession(session);
            }
        });
    }
}
