package com.tianmasys.ebu.qingma.config.properties;

import com.tianmasys.ebu.qingma.config.RouteConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "biz")
@Setter
@Getter
public class RouteConfigProperties {


    private  List<RouteConfig> routes;

}