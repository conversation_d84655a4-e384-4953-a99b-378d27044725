package com.tianmasys.ebu.qingma.controller.app;

import com.tianmasys.ebu.common.core.utils.ValidatorUtils;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.utils.SecurityUtils;
import com.tianmasys.ebu.qingma.service.ICustRelationService;
import com.tianmasys.ebu.qingma.vo.CustRelationVO;
import com.tianmasys.ebu.qingma.vo.SubCustVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 客户关联关系Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-02-21
 */
@RestController
@RequestMapping("/app/custRelation")
public class CustRelationAppController extends BaseController
{
    @Autowired
    private ICustRelationService custRelationService;

    /**
     * 查询客户关联关系列表
     */
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<CustRelationVO>> list(CustRelationVO custRelationVO)
    {
        startPage();
        String custId = SecurityUtils.getCustId();
        custRelationVO.setCustId(custId);
        List<CustRelationVO> list = custRelationService.selectCustRelationList(custRelationVO);
        return success(getDataTable(list));
    }



    /**
     * 获取客户关联关系详细信息
     */
    @GetMapping(value = "/{custId}")
    public AjaxResult<CustRelationVO> getInfo(@PathVariable("custId") String custId)
    {
        return success(custRelationService.selectCustRelationByCustId(custId));
    }

    /**
     * 新增客户关联关系
     */
    @Log(title = "客户关联关系", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<CustRelationVO> add(@Valid @RequestBody CustRelationVO custRelationVO)
    {
        return toAjax(custRelationService.insertCustRelation(custRelationVO));
    }

    /**
     * 修改客户关联关系
     */
    @Log(title = "客户关联关系", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<CustRelationVO> edit(@Valid @RequestBody CustRelationVO custRelationVO)
    {
        String appId = SecurityUtils.getAppId();
        custRelationVO.setAppId(appId);
        return toAjax(custRelationService.updateCustRelation(custRelationVO));
    }

    /**
     * 删除客户关联关系
     */
    @Log(title = "客户关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{custIds}")
    public AjaxResult<CustRelationVO> remove(@PathVariable String[] custIds)
    {
        return toAjax(custRelationService.deleteCustRelationByCustIds(custIds));
    }
    /**
     * 删除客户关联关系(单笔)
     */
    @Log(title = "客户关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteCustRelationByCustId/{custId}")
    public AjaxResult<CustRelationVO> deleteCustRelationByCustId(@PathVariable String custId)
    {
        return toAjax(custRelationService.deleteCustRelationByCustId(custId));
    }

    /**
     * 新增客户关联关系
     */
    @Log(title = "添加子账号", businessType = BusinessType.INSERT)
    @PostMapping("/addSubCust")
    public AjaxResult<CustRelationVO> addSubCust( @RequestBody SubCustVo subCustVo)
    {
        String appId = SecurityUtils.getAppId();
        subCustVo.setAppId(appId);
        //手工校验
        ValidatorUtils.validateEntity(subCustVo);
        return toAjax(custRelationService.addSubCust(subCustVo));
    }
}
