package com.tianmasys.ebu.qingma.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.tianmasys.ebu.common.core.constant.SecurityConstants;
import com.tianmasys.ebu.common.core.utils.DateUtils;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.common.redis.service.RedisService;
import com.tianmasys.ebu.module.system.service.ISysConfigService;
import com.tianmasys.ebu.system.api.RemoteMessageTemplateFeignService;
import com.tianmasys.ebu.system.api.domain.SendMessageDto;
import com.tianmasys.ebu.qingma.constant.IotConst;
import com.tianmasys.ebu.qingma.domain.DeviceEntity;
import com.tianmasys.ebu.qingma.domain.DeviceExtendConfig;
import com.tianmasys.ebu.qingma.domain.ProductEntity;
import com.tianmasys.ebu.qingma.service.CommonCustService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.DeviceEventMapper;
import com.tianmasys.ebu.qingma.service.IDeviceEventService;
import com.tianmasys.ebu.qingma.vo.DeviceEventVO;
import com.tianmasys.ebu.qingma.domain.DeviceEventEntity;

/**
 * iot设备事件表Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-19
 */
@Slf4j
@Service
public class DeviceEventServiceImpl extends ServiceImpl<DeviceEventMapper,DeviceEventEntity> implements IDeviceEventService 
{
    @Autowired
    private DeviceEventMapper deviceEventMapper;

    @Autowired
    private RemoteMessageTemplateFeignService remoteMessageTemplateFeignService;

    @Autowired
    private CommonCustService commonCustService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisService redisService;

    /**
     * 查询iot设备事件表详情
     * 
     * @param id iot设备事件表主键
     * @return iot设备事件表
     */
    @Override
    public DeviceEventVO selectDeviceEventById(Long id)
    {
        DeviceEventVO deviceEventVO = new DeviceEventVO();
        deviceEventVO.setId(id);
        DeviceEventEntity entity = deviceEventMapper.selectJoinOne(DeviceEventEntity.class,getWrapper(deviceEventVO));
        return BeanUtil.toBean(entity,DeviceEventVO.class);
    }

    /**
     * 查询iot设备事件表列表
     * 
     * @param deviceEventVO iot设备事件表
     * @return iot设备事件表
     */
    @Override
    public List<DeviceEventVO> selectDeviceEventList(DeviceEventVO deviceEventVO)
    {
        MPJLambdaWrapper<DeviceEventEntity> wrapper = getWrapper(deviceEventVO);
        wrapper.orderByDesc(DeviceEventEntity::getId);
        List<DeviceEventEntity> deviceEventEntities = deviceEventMapper.selectJoinList(DeviceEventEntity.class,wrapper);
        return PageObjectConvertUtil.convert(deviceEventEntities,DeviceEventVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private MPJLambdaWrapper<DeviceEventEntity> getWrapper(DeviceEventVO query){

        MPJLambdaWrapper<DeviceEventEntity> wrapper = new MPJLambdaWrapper<DeviceEventEntity>()
                .selectAll(DeviceEventEntity.class) // 查询DeviceEntity类的所有字段
                .selectAs(ProductEntity::getName, DeviceEventEntity::getProductName)// 查询ProductEntity类的name字段
                .leftJoin(ProductEntity.class, ProductEntity::getId, DeviceEntity::getProductId); // 左连接


        wrapper.like(ObjUtil.isNotEmpty(query.getSearchValue()), DeviceEventEntity::getDeviceName, query.getSearchValue())
                .or()
                .like(ObjUtil.isNotEmpty(query.getSearchValue()), DeviceEventEntity::getNickname, query.getSearchValue());

        wrapper.eq(ObjUtil.isNotEmpty(query.getId()), DeviceEventEntity::getId, query.getId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceId()), DeviceEventEntity::getDeviceId, query.getDeviceId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceKey()), DeviceEventEntity::getDeviceKey, query.getDeviceKey());
        wrapper.like(ObjUtil.isNotEmpty(query.getDeviceName()), DeviceEventEntity::getDeviceName, query.getDeviceName());
        wrapper.like(ObjUtil.isNotEmpty(query.getNickname()), DeviceEventEntity::getNickname, query.getNickname());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProductId()), DeviceEventEntity::getProductId, query.getProductId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProductKey()), DeviceEventEntity::getProductKey, query.getProductKey());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceType()), DeviceEventEntity::getDeviceType, query.getDeviceType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getEventType()), DeviceEventEntity::getEventType, query.getEventType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getEventIsPush()), DeviceEventEntity::getEventIsPush, query.getEventIsPush());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMsgTemplateId()), DeviceEventEntity::getMsgTemplateId, query.getMsgTemplateId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getEventMsg()), DeviceEventEntity::getEventMsg, query.getEventMsg());
//        wrapper.eq(ObjUtil.isNotEmpty(query.getEventTime()), DeviceEventEntity::getEventTime, query.getEventTime());
        wrapper.between(ArrayUtil.isAllNotEmpty(query.getBeginEventTime(), query.getEndEventTime()), DeviceEventEntity::getEventTime, query.getBeginEventTime(), query.getEndEventTime());

        //查询名下设备列表
        if(StringUtils.isNotBlank(query.getCustId())){
            List<String> deviceKeyList = commonCustService.selectAllDeviceId(query.getCustId());
            wrapper.in(!deviceKeyList.isEmpty(),DeviceEventEntity::getDeviceKey, deviceKeyList);
            wrapper.eq(deviceKeyList.isEmpty(),DeviceEventEntity::getDeviceKey, "");
        }
        return wrapper;
    }

    /**
     * 新增iot设备事件表
     * 
     * @param deviceEventVO iot设备事件表
     * @return 结果
     */
    @Override
    public int insertDeviceEvent(DeviceEventVO deviceEventVO)
    {
       DeviceEventEntity entity = BeanUtil.toBean(deviceEventVO, DeviceEventEntity.class);
       return deviceEventMapper.insert(entity);
    }

    /**
     * 修改iot设备事件表
     * 
     * @param deviceEventVO iot设备事件表
     * @return 结果
     */
    @Override
    public int updateDeviceEvent(DeviceEventVO deviceEventVO)
    {
       DeviceEventEntity entity = BeanUtil.toBean(deviceEventVO, DeviceEventEntity.class);
        return deviceEventMapper.updateById(entity);
    }

    /**
     * 批量删除iot设备事件表
     * 
     * @param ids 需要删除的iot设备事件表主键
     * @return 结果
     */
    @Override
    public int deleteDeviceEventByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return deviceEventMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除iot设备事件表信息
     * 
     * @param id iot设备事件表主键
     * @return 结果
     */
    @Override
    public int deleteDeviceEventById(Long id)
    {

        return deviceEventMapper.deleteById(id);
    }

    /**
     * 保存设备消息
     *
     * @param deviceEntity 设备实体
     * @param eventTime 事件时间
     * @param eventType 事件类型:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;
     * @param eventContent 事件内容
     * @return 结果
     */
    @Override
    @Async
    public void consumerDeviceEvent(DeviceEntity deviceEntity, Date eventTime,String eventType, String eventContent) {
        String resStr = eventContent;
//        if("0".equals(eventType)){
//            JSONObject res = JSONObject.parseObject(eventContent);
//            String ct = res.getString("ct");
//            String[]  tmps = ct.split("\\|");
//            if(tmps.length == 3){
//                res.put("csp",tmps[0]);
//                res.put("pinState",tmps[1]);
//                res.put("waterLevel",tmps[2]);
//            }
//            resStr = res.toJSONString();
//
//        }
        DeviceExtendConfig extendConfig = deviceEntity.getExtendConfig();

        String isPush = extendConfig.getIsPush();

        DeviceEventVO deviceEventVO = BeanUtil.copyProperties(deviceEntity, DeviceEventVO.class,"id","deleteFlag","createTime","createBy","updateTime","updateBy","tenantId");
        deviceEventVO.setDeviceId(deviceEntity.getId());
        deviceEventVO.setEventTime(eventTime);
        deviceEventVO.setEventType(eventType); //事件类型:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;
        deviceEventVO.setEventMsg(resStr);

        if("1".equals(isPush)){ //推送
            String time = configService.selectConfigByKey("iot.device.msg.time");
            deviceEventVO.setEventIsPush(isPush);
            String templateId = extendConfig.getTemplateId();
            List<String> eventTypeList = extendConfig.getEventTypeList();
            String isPushParentFlag = extendConfig.getIsPushParentFlag();
            String custId = deviceEntity.getCustId();
            List<String> allCustId = commonCustService.selectParentCustIdsByCustId(custId); //获取上级客户id
            List<String> custIdList = new ArrayList<>();

            //不推送上级用户
            if("0".equals(isPushParentFlag)){
                custIdList.add(custId);
            }else{
                custIdList.addAll(allCustId);
            }

            if(eventTypeList !=null && eventTypeList.contains(eventType)){

                for(String tmpCustId: custIdList){
//                    String msgRedisKey = String.format("%s:%s_%s_%s","deviceMsg",deviceEntity.getDeviceName(),eventType,tmpCustId);
                    String msgRedisKey = commonCustService.getMsgKey(deviceEntity.getDeviceName(),eventType,tmpCustId);
                    if(!redisService.hasKey(msgRedisKey)){
                        deviceEventVO.setMsgTemplateId(Long.valueOf(templateId));
                        SendMessageDto dto = new SendMessageDto();
                        dto.setTemplateId(templateId);
                        //TODO 设备关联
                        dto.setCustId(tmpCustId);
                        JSONObject data =  new JSONObject();
                        data.put("deviceName",deviceEntity.getNickname());
                        data.put("deviceId",deviceEntity.getDeviceName());
                        data.put("alarmContent", IotConst.DeviceEventType.getNameByKey(eventType));
                        data.put("alarmTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,eventTime));
                        dto.setData(data);
                        remoteMessageTemplateFeignService.sendTempMsg(dto, SecurityConstants.INNER);
                        redisService.setCacheObject(msgRedisKey,deviceEntity.getDeviceName(),Long.valueOf(time), TimeUnit.SECONDS);
                    }


                }


            }else{
                deviceEventVO.setEventIsPush("0");
            }




        }else {
            deviceEventVO.setEventIsPush("0");
        }
        int count = insertDeviceEvent(deviceEventVO);
        log.info("添加事件记录：{}",count);
    }


}
