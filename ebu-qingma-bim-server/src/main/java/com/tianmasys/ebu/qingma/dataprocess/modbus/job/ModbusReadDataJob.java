package com.tianmasys.ebu.qingma.dataprocess.modbus.job;

import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.MasterData;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.ModbusConfig;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.SlaveData;
import com.tianmasys.ebu.qingma.dataprocess.modbus.service.ModbusService;
import com.tianmasys.ebu.qingma.dataprocess.modbus.callback.ModbusDataCallback;
import com.tianmasys.ebu.qingma.job.scheduler.TaskSchedulerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Modbus定时读取任务管理器
 */
@Slf4j
@Component
public class ModbusReadDataJob {

    @Autowired
    private ModbusService modbusService;

    @Autowired
    private ModbusConfig modbusConfig;

    @Autowired
    private TaskSchedulerManager taskSchedulerManager;

    // 线程池用于并发读取多个从站数据
    private ExecutorService executorService;

    // 存储每个站点对应的从站ID集合
    private Map<Integer, Set<Integer>> siteSlaveMap = new ConcurrentHashMap<>();

    // 存储站点对应的所有从站任务
    private Map<Integer, List<SlaveReadTask>> siteSlaveTasks = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        // 初始化线程池
        executorService = Executors.newFixedThreadPool(5);

        // 构建站点与从站的映射关系
        modbusConfig.getMasters().forEach(masterData -> {
            masterData.getSlaves().forEach(slaveData -> {
                if (slaveData.isAutoRead()) {
                    siteSlaveMap.computeIfAbsent(masterData.getSiteId(), k -> ConcurrentHashMap.newKeySet())
                            .add(slaveData.getSlaveId());
                }
            });
        });

        // 构建站点与从站任务的映射关系
        modbusConfig.getMasters().stream()
                .collect(Collectors.groupingBy(MasterData::getSiteId))
                .forEach((siteId, masterDataList) -> {
                    List<SlaveReadTask> slaveReadTasks = masterDataList.stream()
                            .flatMap(masterData -> {
                                ModbusClient modbusClient = modbusConfig.getClientByMasterId(masterData.getId());
                                return masterData.getSlaves().stream()
                                        .filter(SlaveData::isAutoRead)
                                        .map(slaveData -> new SlaveReadTask(modbusClient, slaveData));
                            })
                            .collect(Collectors.toList());

                    siteSlaveTasks.put(siteId, slaveReadTasks);
                });

        // 按siteId分组注册定时任务
        modbusConfig.getMasters().stream()
                .collect(Collectors.groupingBy(MasterData::getSiteId))
                .forEach((siteId, masterDataList) -> {
                    // 只为包含自动读取从站的站点注册任务
                    boolean hasAutoReadSlaves = masterDataList.stream()
                            .flatMap(masterData -> masterData.getSlaves().stream())
                            .anyMatch(SlaveData::isAutoRead);

                    if (hasAutoReadSlaves) {
                        String taskName = "modbus-site-" + siteId;
                        // 使用组内最小间隔作为调度间隔
                        long minInterval = masterDataList.stream()
                                .flatMap(masterData -> masterData.getSlaves().stream())
                                .filter(SlaveData::isAutoRead)
                                .mapToLong(SlaveData::getInterval)
                                .min()
                                .orElse(5000L);

                        taskSchedulerManager.registerFixedDelayTask(taskName,
                                () -> readAndProcessDataBySite(siteId),
                                0,
                                minInterval);

                        log.info("已注册站点 {} 的读取任务，周期: {}ms", siteId, minInterval);
                    }
                });
    }

    /**
     * 按站点读取并处理数据
     * @param siteId 站点ID
     */
    private void readAndProcessDataBySite(Integer siteId) {
        try {
//            log.debug("开始读取站点 {} 的所有数据", siteId);

            List<SlaveReadTask> slaveReadTasks = siteSlaveTasks.get(siteId);
            if (slaveReadTasks == null || slaveReadTasks.isEmpty()) {
                log.debug("站点 {} 没有需要读取的从站", siteId);
                return;
            }

            // 并发读取所有从站数据
            Map<SlaveData, Map<String, Object>> allData = readAllSlavesConcurrently(slaveReadTasks);

            // 统一处理所有数据
            processAllSiteData(siteId, allData);

//            log.debug("站点 {} 的所有数据读取和处理完成", siteId);
        } catch (Exception e) {
            log.error("读取站点 " + siteId + " 数据时发生异常", e);
        }
    }

    /**
     * 并发读取所有从站数据
     * @param slaveReadTasks 从站读取任务列表
     * @return 从站数据映射
     */
    private Map<SlaveData, Map<String, Object>> readAllSlavesConcurrently(List<SlaveReadTask> slaveReadTasks) {
        CountDownLatch latch = new CountDownLatch(slaveReadTasks.size());
        Map<SlaveData, Map<String, Object>> allData = new ConcurrentHashMap<>();

        for (SlaveReadTask task : slaveReadTasks) {
            executorService.submit(() -> {
                try {
                    ModbusClient modbusClient = task.getModbusClient();
                    Map<String, Object> data = modbusService.readData(modbusClient, task.getSlaveData());
                    allData.put(task.getSlaveData(), data);
//                    log.debug("站点 {} 主站 {} 从站 {} 数据读取完成",
//                            modbusClient.getMasterData().getSiteId(),
//                            modbusClient.getMasterData().getId(),
//                            task.getSlaveData().getSlaveId());
                } catch (Exception e) {
                    log.error("从站 " + task.getSlaveData().getSlaveId() + " 读取数据异常", e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            // 等待所有从站读取完成
            latch.await();
//            log.debug("所有从站数据读取完成，共 {} 个从站", slaveReadTasks.size());
        } catch (InterruptedException e) {
            log.error("等待所有从站数据读取完成时被中断", e);
            Thread.currentThread().interrupt();
        }

        return allData;
    }

    /**
     * 统一处理站点的所有数据
     * @param siteId 站点ID
     * @param allData 所有从站数据
     */
    private void processAllSiteData(Integer siteId, Map<SlaveData, Map<String, Object>> allData) {
//        log.info("统一处理站点 {} 的所有从站数据，共 {} 个从站", siteId, allData.size());

        // 遍历所有数据并处理
        allData.forEach((slaveData, data) -> {
            try {

                SlaveReadTask slaveReadTask = findSlaveReadTaskBySlaveData(slaveData);
                ModbusClient modbusClient = slaveReadTask.getModbusClient();
                // 处理数据
                processData( modbusClient,slaveData, data);

                // 执行回调
                executeCallback( modbusClient,slaveData, data);
            } catch (Exception e) {
                log.error("处理从站 " + slaveData.getSlaveId() + " 数据时发生异常", e);
            }
        });
    }



    /**
     * 处理数据（可在此添加通用数据处理逻辑）
     * @param slaveData 从站配置数据
     * @param data 读取到的原始数据
     */
    private void processData( ModbusClient modbusClient, SlaveData slaveData, Map<String, Object> data) {
        // 可以在这里添加通用的数据处理逻辑
        // 例如：数据格式化、单位转换等
    }

    /**
     * 执行回调
     * @param slaveData 从站配置数据
     * @param data 处理后的数据
     */
    private void executeCallback( ModbusClient modbusClient,SlaveData slaveData, Map<String, Object> data) {
        Integer slaveId = slaveData.getSlaveId();
        MasterData masterData = modbusClient.getMasterData();
        ModbusDataCallback callback = modbusClient.getCallback(slaveId);
        if(callback != null ){
            callback.onDataReceived(masterData, slaveData, data);
        }
    }

    /**
     * 根据SlaveData查找对应的SlaveReadTask
     * @param slaveData 从站数据配置
     * @return 对应的SlaveReadTask，如果未找到则返回null
     */
    private SlaveReadTask findSlaveReadTaskBySlaveData(SlaveData slaveData) {
        if (slaveData == null) {
            return null;
        }

        return siteSlaveTasks.values().stream()
                .flatMap(List::stream)
                .filter(task -> task.getSlaveData().equals(slaveData))
                .findFirst()
                .orElse(null);
    }


    /**
     * 从站读取任务封装类
     */
    private static class SlaveReadTask {
        private final ModbusClient modbusClient;
        private final SlaveData slaveData;

        public SlaveReadTask(ModbusClient modbusClient, SlaveData slaveData) {
            this.modbusClient = modbusClient;
            this.slaveData = slaveData;
        }

        public ModbusClient getModbusClient() {
            return modbusClient;
        }

        public SlaveData getSlaveData() {
            return slaveData;
        }
    }

    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
