package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.ProductVO;
import com.tianmasys.ebu.qingma.service.IProductService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * iot产品表Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-01-17
 */
@RestController
@RequestMapping("/product")
public class ProductController extends BaseController
{
    @Autowired
    private IProductService productService;

    /**
     * 查询iot产品表列表
     */
    @RequiresPermissions("iot:product:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<ProductVO>> list(ProductVO productVO)
    {
        startPage();
        List<ProductVO> list = productService.selectProductList(productVO);
        return success(getDataTable(list));
    }


    /**
     * 导出iot产品表列表
     */
    @RequiresPermissions("iot:product:export")
    @Log(title = "iot产品表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductVO productVO)
    {
        List<ProductVO> list = productService.selectProductList(productVO);
        ExcelUtil<ProductVO> util = new ExcelUtil<ProductVO>(ProductVO.class);
        util.exportExcel(response, list, "iot产品表数据");
    }

    /**
     * 获取iot产品表详细信息
     */
    @RequiresPermissions("iot:product:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<ProductVO> getInfo(@PathVariable("id") Long id)
    {
        return success(productService.selectProductById(id));
    }

    /**
     * 新增iot产品表
     */
    @RequiresPermissions("iot:product:add")
    @Log(title = "iot产品表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<ProductVO> add(@Valid @RequestBody ProductVO productVO)
    {
        return toAjax(productService.insertProduct(productVO));
    }

    /**
     * 修改iot产品表
     */
    @RequiresPermissions("iot:product:edit")
    @Log(title = "iot产品表", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<ProductVO> edit(@Valid @RequestBody ProductVO productVO)
    {
        return toAjax(productService.updateProduct(productVO));
    }

    /**
     * 删除iot产品表
     */
    @RequiresPermissions("iot:product:remove")
    @Log(title = "iot产品表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<ProductVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(productService.deleteProductByIds(ids));
    }
}
