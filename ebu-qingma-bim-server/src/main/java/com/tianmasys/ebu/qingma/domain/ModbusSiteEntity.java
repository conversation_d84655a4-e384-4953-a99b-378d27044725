package com.tianmasys.ebu.qingma.domain;

import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * modbus站点
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Data
@TableName("modbus_site")
public class ModbusSiteEntity extends BaseEntity {
	/**
	* 主键ID
	*/
	@TableId("id")
	private Long id;

	/**
	* 站点id
	*/
    @TableField("site_id")
	private Long siteId;

	/**
	* 站点名称
	*/
    @TableField("site_name")
	private String siteName;

	/**
	* 站点描述
	*/
    @TableField("description")
	private String description;

	/**
	* 主站点id
	*/
    @TableField("master_id")
	private Long masterId;

	/**
	* 主机地址
	*/
    @TableField("host")
	private String host;

	/**
	* 端口
	*/
    @TableField("port")
	private Integer port;

	/**
	* 是否长连接：0-否；1-是；
	*/
    @TableField("keep_alive")
	private Boolean keepAlive;


	/** 是否删除：0-正常；1-删除； */
	@TableField(fill = FieldFill.INSERT)
	@TableLogic
	private String deleteFlag;

}