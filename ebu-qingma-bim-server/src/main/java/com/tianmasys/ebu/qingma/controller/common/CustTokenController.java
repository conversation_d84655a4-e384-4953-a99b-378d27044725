package com.tianmasys.ebu.qingma.controller.common;

import com.alibaba.fastjson2.JSONObject;
import com.tianmasys.ebu.common.core.domain.R;
import com.tianmasys.ebu.common.core.utils.JwtUtils;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.common.security.auth.AuthUtil;
import com.tianmasys.ebu.common.security.service.CustTokenService;
import com.tianmasys.ebu.common.security.utils.SecurityUtils;
import com.tianmasys.ebu.system.api.model.LoginCust;
import com.tianmasys.ebu.qingma.service.CustLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 功能描述：客户登录
 *
 * <AUTHOR>
 * @date 2023/05/10
 **/
@RestController
@RequestMapping("/cust")
public class CustTokenController {

    @Autowired
    private CustTokenService custTokenService;

    @Autowired
    private CustLoginService custLoginService;

    @PostMapping("/login")
    public R<?> login(@RequestBody JSONObject param, @RequestHeader("appId") String appId)
    {
        String code = param.getString("code");
        String authType = param.getString("authType"); //认证方式：01-密码登录
        Boolean isRegister = param.getBoolean("isRegister");
        LoginCust custInfo = null;

        if(StringUtils.isNotBlank(authType)){
            // 用户登录
            custInfo = custLoginService.loginForAuth(appId,authType,param);

        }else if(StringUtils.isNotBlank(code)){
            // 用户登录
            custInfo = custLoginService.loginForCode(code,appId,isRegister);
            if(StringUtils.isBlank(custInfo.getCustId())){
                JSONObject res = new JSONObject();
                res.put("openId", custInfo.getOpenId());
                res.put("unionId", custInfo.getUnionId());
                return R.ok(res);
            }
        }

        // 获取登录token
        return R.ok(custTokenService.createToken(custInfo,appId));
    }



    @PostMapping("/refresh")
    public R<?> refresh(HttpServletRequest request)
    {
        LoginCust loginCust = custTokenService.getLoginCust(request);
        if (StringUtils.isNotNull(loginCust))
        {
            // 刷新令牌有效期
            custTokenService.refreshToken(loginCust);
            return R.ok();
        }
        return R.ok();
    }

    @DeleteMapping("/logout")
    public R<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
//            // 记录用户退出日志
//            custTokenService.logout(username);
        }
        return R.ok();
    }



}