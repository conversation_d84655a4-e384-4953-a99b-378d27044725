package com.tianmasys.ebu.qingma.dataprocess.modbus.config;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

@Data
/*
 * 从站数据定义
 */
public class SlaveData {

    /*
     * 从站ID
     */
    private Integer slaveId;

    /*
     * 从站数据地址列表
     */
    private List<Integer> offsetList;

    /*
     * 是否自动读取
     */
    private boolean autoRead = false;

    /*
     * 读取间隔时间(毫秒)
     */
    private long interval = 5000;

    /**
     * 功能码：1-COIL_STATUS;2-INPUT_STATUS;3-HOLDING_REGISTER;4-INPUT_REGISTER
     */
    private String functionCode;


    /**
     * 数据类型:2-TWO_BYTE_INT_UNSIGNED;3-TWO_BYTE_INT_SIGNED;
     */
    private String dataType;


    private String mappingConfig;

//    /*
//     * 回调类名
//     */
//    private String callbackClass;


//    public void setOffset(String offset) {
//        this.offsetList = new LinkedList<>();
//
//        Arrays.stream(offset.split(",")).forEach(item -> {
//            this.offsetList.add(Integer.parseInt(item));
//        });
//    }

}
