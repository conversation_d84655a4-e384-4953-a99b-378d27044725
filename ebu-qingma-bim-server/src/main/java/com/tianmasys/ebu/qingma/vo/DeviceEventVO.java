package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * iot设备事件表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-19
 */
@Data
@Schema(description = "iot设备事件表")
public class DeviceEventVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "设备 ID")
	private Long id;
	
	@Schema(description = "设备id")
	@NotEmpty(message = "设备id不能为空!")
	private Long deviceId;
	
	@Schema(description = "设备唯一标识符(全局唯一，用于识别设备)")
	@NotEmpty(message = "设备唯一标识符不能为空!")
	private String deviceKey;
	
	@Schema(description = "设备名称(在产品内唯一，用于标识设备)")
	@NotEmpty(message = "设备名称不能为空!")
	private String deviceName;
	
	@Schema(description = "设备备注名称")
	@NotEmpty(message = "设备备注名称不能为空!")
	private String nickname;
	
	@Schema(description = "产品编号")
	@NotEmpty(message = "产品编号不能为空!")
	private Long productId;
	
	@Schema(description = "产品标识")
	@NotEmpty(message = "产品标识不能为空!")
	private String productKey;
	
	@Schema(description = "设备类型:0-直连设备;1-网关子设备;2-网关设备;")
	@NotEmpty(message = "设备类型不能为空!")
	private String deviceType;
	
	@Schema(description = "事件类型:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;")
	@NotEmpty(message = "事件类型不能为空!")
	private String eventType;
	
	@Schema(description = "是否推送:0-不推送;1-推送;")
	@NotEmpty(message = "是否推送不能为空!")
	private String eventIsPush;
	
	@Schema(description = "模板消息id")
	@NotEmpty(message = "模板消息id不能为空!")
	private Long msgTemplateId;
	
	@Schema(description = "事件报文")
	@NotEmpty(message = "事件报文不能为空!")
	private String eventMsg;
	
	@Schema(description = "事件时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotEmpty(message = "事件时间不能为空!")
	private Date eventTime;
	
	@Schema(description = "是否删除：0-正常；1-删除；")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;

	@Schema(description = "产品名称")
	private String productName;


	@Schema(description = "事件时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotEmpty(message = "事件时间不能为空!")
	private Date beginEventTime;

	@Schema(description = "事件时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotEmpty(message = "事件时间不能为空!")
	private Date endEventTime;


	@Schema(description = "搜索内容")
	private String searchValue;

	@Schema(description = "客户id")
	private String custId;


}