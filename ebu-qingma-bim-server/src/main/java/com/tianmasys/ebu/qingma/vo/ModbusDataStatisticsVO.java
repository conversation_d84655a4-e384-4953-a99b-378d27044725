package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * modbus统计数据统计表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-07
 */
@Data
@Schema(description = "modbus统计数据统计表")
public class ModbusDataStatisticsVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键ID")
	private Long id;
	
	@Schema(description = "站点id")
	@NotEmpty(message = "站点id不能为空!")
	private Long siteId;
	
	@Schema(description = "统计类型：minute-分钟；hour-小时；day-天")
	@NotEmpty(message = "统计类型不能为空!")
	private String statType;
	
	@Schema(description = "统计开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotEmpty(message = "统计开始时间不能为空!")
	private Date startTime;
	
	@Schema(description = "统计结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotEmpty(message = "统计结束时间不能为空!")
	private Date endTime;
	
	@Schema(description = "统计后的数据")
	@NotEmpty(message = "统计后的数据不能为空!")
	private Object data;
	
	@Schema(description = "是否删除：0-正常；1-删除；")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;
	



}