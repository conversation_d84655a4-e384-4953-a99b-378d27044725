package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 历史数据查询请求参数
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-08
 */
@Data
@Schema(description = "历史数据查询请求参数")
public class HistoryDataRequestVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "站点ID", required = true)
    @NotNull(message = "站点ID不能为空!")
    private Long siteId;

    @Schema(description = "数据类型：flow-流量、humidity-湿度、pressure-压力、temperature-温度", required = true)
    @NotEmpty(message = "数据类型不能为空!")
    @Pattern(regexp = "^(flow|humidity|pressure|temperature)$", message = "数据类型只能是flow、humidity、pressure、temperature中的一种")
    private String dataType;

    @Schema(description = "时间周期：1h-1小时、6h-6小时、12h-12小时、24h-24小时、7d-7天", required = true)
    @NotEmpty(message = "时间周期不能为空!")
    @Pattern(regexp = "^(1h|6h|12h|24h|7d)$", message = "时间周期只能是1h、6h、12h、24h、7d中的一种")
    private String period;
}
