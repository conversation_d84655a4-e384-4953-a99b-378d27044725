package com.tianmasys.ebu.qingma.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.tianmasys.ebu.qingma.vo.ModbusDataLogVO;
import com.tianmasys.ebu.qingma.domain.ModbusDataLogEntity;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * modbus点位数据记录表Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */
public interface IModbusDataLogService extends IService<ModbusDataLogEntity>
{
    /**
     * 查询modbusDataLog
     * 
     * @param id modbusDataLog主键
     * @return modbusDataLog
     */
    public ModbusDataLogVO selectModbusDataLogById(Long id);

    /**
     * 查询modbusDataLog列表
     * 
     * @param modbusDataLogVO modbusDataLog
     * @return modbusDataLog集合
     */
    public List<ModbusDataLogVO> selectModbusDataLogList(ModbusDataLogVO modbusDataLogVO);

    /**
     * 新增modbusDataLog
     * 
     * @param modbusDataLogVO modbusDataLog
     * @return 结果
     */
    public int insertModbusDataLog(ModbusDataLogVO modbusDataLogVO);

    /**
     * 修改modbusDataLog
     * 
     * @param modbusDataLogVO modbusDataLog
     * @return 结果
     */
    public int updateModbusDataLog(ModbusDataLogVO modbusDataLogVO);

    /**
     * 批量删除modbusDataLog
     * 
     * @param ids 需要删除的modbusDataLog主键集合
     * @return 结果
     */
    public int deleteModbusDataLogByIds(Long[] ids);

    /**
     * 删除modbusDataLog信息
     * 
     * @param id modbusDataLog主键
     * @return 结果
     */
    public int deleteModbusDataLogById(Long id);

    /**
     *  异步-批量保存modbusDataLog
     * @param jsonObject
     * @return
     */
    public Integer asyncSaveModbusDataLog(JSONObject jsonObject);
}
