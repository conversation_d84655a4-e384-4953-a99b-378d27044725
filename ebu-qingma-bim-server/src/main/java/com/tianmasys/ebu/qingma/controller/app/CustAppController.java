package com.tianmasys.ebu.qingma.controller.app;

import com.tianmasys.ebu.common.core.domain.R;
import com.tianmasys.ebu.common.security.utils.SecurityUtils;
import com.tianmasys.ebu.module.system.domain.CustOpenChannel;
import com.tianmasys.ebu.module.system.service.ICustOpenChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  app端用户登录
 */
@RestController
@RequestMapping("/app/cust")
public class CustAppController {

    @Autowired
    private ICustOpenChannelService custOpenChannelService;

    @PostMapping("/updateCustOpenChannel")
    public R<?> updateCustOpenChannel(@RequestBody CustOpenChannel custOpenChannel, @RequestHeader("appId") String appId)
    {
        String custId = SecurityUtils.getCustId();
        custOpenChannel.setCustId(custId);
        int count = custOpenChannelService.updateCustOpenChannel(custOpenChannel);

        // 获取登录token
        return R.ok(count);
    }
}
