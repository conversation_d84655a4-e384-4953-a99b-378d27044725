package com.tianmasys.ebu.qingma.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 历史数据查询响应结果
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-08
 */
@Data
@Schema(description = "历史数据查询响应结果")
public class HistoryDataResponseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "站点ID")
    private Long siteId;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "时间周期")
    private String period;

    @Schema(description = "查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(description = "历史数据列表")
    private List<HistoryDataItem> dataList;

    /**
     * 历史数据项
     */
    @Data
    @Schema(description = "历史数据项")
    public static class HistoryDataItem implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "时间点")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date time;

        @Schema(description = "平均值")
        private Double avgValue;

        @Schema(description = "最新值")
        private Object lastValue;

        @Schema(description = "数据点数量")
        private Integer count;

        @Schema(description = "数据来源：database-数据库、memory-内存")
        private String source;

        @Schema(description = "单位")
        private Object unit;

        @Schema(description = "状态")
        private Object status;

        @Schema(description = "趋势")
        private Object trend;
    }
}
