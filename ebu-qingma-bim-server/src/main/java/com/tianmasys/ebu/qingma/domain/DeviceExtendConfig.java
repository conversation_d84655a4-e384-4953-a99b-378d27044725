package com.tianmasys.ebu.qingma.domain;

import lombok.Data;

import java.util.List;

@Data
public class DeviceExtendConfig {

    /**
     *  是否推送：0-不推送;1-推送;
     */
    private String isPush;

    /**
     *  模板id
     */
    private String templateId;

    /**
     * 事件类型列表:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;
     */
    private List<String> eventTypeList;

    /**
     * 是否推送父节点：0-不推送;1-推送;
     */
    private String isPushParentFlag;

//    /**
//     *  绑定custId
//     */
//    private String custId;
}
