package com.tianmasys.ebu.qingma.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 *  emqx 客户端信息-请求
 */
@Data
public class EmqxClientReqVo implements Serializable {

    // 客户端id,模糊搜索
    @JSONField(name = "like_clientid")
    private  String clientid;

    // 分页大小
    @JSONField(name="limit")
    private Integer pageSize;

    // 分页页码
    @JSONField(name="page")
    private Integer pageNum;

    //总条数
    @JSONField(name="count")
    private Integer total;


}
