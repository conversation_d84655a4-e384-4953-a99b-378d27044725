package com.tianmasys.ebu.qingma.mapper;

import com.tianmasys.ebu.common.mybatis.core.mapper.BaseMapperX;
import com.tianmasys.ebu.qingma.domain.CustRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* iot客户关联关系
*
* @<NAME_EMAIL>
* @since 1.0.0 2025-02-21
*/
@Mapper
public interface CustRelationMapper extends BaseMapperX<CustRelationEntity> {


    public List<CustRelationEntity> selectCustRelationList(@Param("custRelation")CustRelationEntity custRelation,@Param("custIdList")List<String> custIdList);

    public CustRelationEntity selectCustRelationByCustId(String custId);

    /**
     * 通过客户号查询所有下级
     * @param custId
     * @return
     */
    public List<String> selectSubCustIdsByCustId(@Param("custId") String custId);

    /**
     *  通过客户号查询所有上级
     * @param custId
     * @return
     */
    public List<String> selectParentCustIdsByCustId(@Param("custId") String custId);

}