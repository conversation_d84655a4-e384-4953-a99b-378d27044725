package com.tianmasys.ebu.qingma.domain;

import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

/**
 * iot产品物模型功能
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */

@Data
@TableName("iot_think_model_function")
public class ThinkModelFunctionEntity extends BaseEntity {
	/**
	* 设备 ID
	*/
	@TableId
	private Long id;

	/**
	* 设备唯一标识符(全局唯一，用于识别设备)
	*/
	private String identifier;

	/**
	* 功能名称
	*/
	private String name;

	/**
	* 功能描述
	*/
	private String description;

	/**
	* 产品id
	*/
	private Long productId;

	/**
	* 产品标识
	*/
	private String productKey;

	/**
	* 功能类型:0-属性;1-服务;2-事件;
	*/
	private String type;

	/**
	* 属性
	*/
	private String property;

	/**
	* 事件
	*/
	private String event;

	/**
	* 服务
	*/
	private String service;

//	/**
//	* 是否删除：0-正常；1-删除；
//	*/
//	private String deleteFlag;
//
//	/**
//	* 创建时间
//	*/
//	private Date createTime;
//
//	/**
//	* 创建用户id
//	*/
//	private String createBy;
//
//	/**
//	* 修改时间
//	*/
//	private Date updateTime;
//
//	/**
//	* 修改用户id
//	*/
//	private String updateBy;
//
//	/**
//	* 租户编号
//	*/
//	private Long tenantId;

}