package com.tianmasys.ebu.qingma.job.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 通用定时任务调度管理器
 * 用于在Spring Boot应用启动完成后调度各种定时任务
 */
@Slf4j
@Component
public class TaskSchedulerManager {

    /**
     * 使用ScheduledExecutorService实现任务调度
     */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(15);

    /**
     * 存储所有已调度的任务
     */
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    /**
     * 存储待注册的任务配置
     */
    private final Map<String, TaskConfig> pendingTasks = new ConcurrentHashMap<>();

    /**
     * 标记应用是否已准备就绪
     */
    private volatile boolean isReady = false;

    /**
     * 任务配置类
     */
    private static class TaskConfig {
        private final Runnable task;
        private final long initialDelay;
        private final long delay;
        private final TaskType taskType;
        private final long period;

        public TaskConfig(Runnable task, long initialDelay, long delay, TaskType taskType, long period) {
            this.task = task;
            this.initialDelay = initialDelay;
            this.delay = delay;
            this.taskType = taskType;
            this.period = period;
        }

        // Getters
        public Runnable getTask() { return task; }
        public long getInitialDelay() { return initialDelay; }
        public long getDelay() { return delay; }
        public TaskType getTaskType() { return taskType; }
        public long getPeriod() { return period; }
    }

    /**
     * 任务类型枚举
     */
    private enum TaskType {
        FIXED_DELAY,
        FIXED_RATE,
        DELAY
    }

    /**
     * 应用启动完成后触发，初始化所有已注册但尚未调度的任务
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        isReady = true;
        log.info("TaskSchedulerManager已准备就绪");

        // 初始化所有已注册但尚未调度的任务
        if (!pendingTasks.isEmpty()) {
            log.info("开始初始化{}个已注册的任务", pendingTasks.size());
            pendingTasks.forEach((taskName, taskConfig) -> {
                try {
                    scheduleTaskInternal(taskName, taskConfig);
                    log.info("任务 {} 初始化成功", taskName);
                } catch (Exception e) {
                    log.error("初始化任务 {} 时发生错误", taskName, e);
                }
            });
        }
    }

    /**
     * 注册固定延迟的周期性任务（在应用启动完成前调用）
     *
     * @param taskName 任务名称
     * @param task     要执行的任务
     * @param initialDelay 初始延迟时间（毫秒）
     * @param delay    周期间隔（毫秒）
     */
    public void registerFixedDelayTask(String taskName, Runnable task, long initialDelay, long delay) {
        if (taskName == null || taskName.isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (task == null) {
            throw new IllegalArgumentException("任务不能为null");
        }
        if (initialDelay < 0) {
            throw new IllegalArgumentException("初始延迟时间不能小于0");
        }
        if (delay <= 0) {
            throw new IllegalArgumentException("延迟时间必须大于0");
        }

        TaskConfig taskConfig = new TaskConfig(task, initialDelay, delay, TaskType.FIXED_DELAY, 0);
        pendingTasks.put(taskName, taskConfig);
        log.info("已注册固定延迟任务: {}，初始延迟: {}ms，周期: {}ms", taskName, initialDelay, delay);

        // 如果已经准备就绪，立即调度任务
        if (isReady) {
            scheduleTaskInternal(taskName, taskConfig);
        }
    }

    /**
     * 注册固定频率的周期性任务（在应用启动完成前调用）
     *
     * @param taskName 任务名称
     * @param task     要执行的任务
     * @param initialDelay 初始延迟时间（毫秒）
     * @param period   周期时间（毫秒）
     */
    public void registerFixedRateTask(String taskName, Runnable task, long initialDelay, long period) {
        if (taskName == null || taskName.isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (task == null) {
            throw new IllegalArgumentException("任务不能为null");
        }
        if (initialDelay < 0) {
            throw new IllegalArgumentException("初始延迟时间不能小于0");
        }
        if (period <= 0) {
            throw new IllegalArgumentException("周期时间必须大于0");
        }

        TaskConfig taskConfig = new TaskConfig(task, initialDelay, 0, TaskType.FIXED_RATE, period);
        pendingTasks.put(taskName, taskConfig);
        log.info("已注册固定频率任务: {}，初始延迟: {}ms，周期: {}ms", taskName, initialDelay, period);

        // 如果已经准备就绪，立即调度任务
        if (isReady) {
            scheduleTaskInternal(taskName, taskConfig);
        }
    }

    /**
     * 注册一次性延迟任务（在应用启动完成前调用）
     *
     * @param taskName 任务名称
     * @param task     要执行的任务
     * @param delay    延迟时间（毫秒）
     */
    public void registerDelayTask(String taskName, Runnable task, long delay) {
        if (taskName == null || taskName.isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (task == null) {
            throw new IllegalArgumentException("任务不能为null");
        }
        if (delay < 0) {
            throw new IllegalArgumentException("延迟时间不能小于0");
        }

        TaskConfig taskConfig = new TaskConfig(task, delay, 0, TaskType.DELAY, 0);
        pendingTasks.put(taskName, taskConfig);
        log.info("已注册延迟任务: {}，延迟: {}ms", taskName, delay);

        // 如果已经准备就绪，立即调度任务
        if (isReady) {
            scheduleTaskInternal(taskName, taskConfig);
        }
    }

    /**
     * 内部调度任务方法
     *
     * @param taskName 任务名称
     * @param taskConfig 任务配置
     */
    private void scheduleTaskInternal(String taskName, TaskConfig taskConfig) {
        ScheduledFuture<?> scheduledFuture = null;

        switch (taskConfig.getTaskType()) {
            case FIXED_DELAY:
                scheduledFuture = scheduler.scheduleWithFixedDelay(
                        taskConfig.getTask(),
                        taskConfig.getInitialDelay(),
                        taskConfig.getDelay(),
                        TimeUnit.MILLISECONDS
                );
                break;
            case FIXED_RATE:
                scheduledFuture = scheduler.scheduleAtFixedRate(
                        taskConfig.getTask(),
                        taskConfig.getInitialDelay(),
                        taskConfig.getPeriod(),
                        TimeUnit.MILLISECONDS
                );
                break;
            case DELAY:
                scheduledFuture = scheduler.schedule(
                        taskConfig.getTask(),
                        taskConfig.getInitialDelay(),
                        TimeUnit.MILLISECONDS
                );
                break;
        }

        if (scheduledFuture != null) {
            scheduledTasks.put(taskName, scheduledFuture);
            pendingTasks.remove(taskName);
        }
    }

    /**
     * 调度固定延迟的周期性任务（直接调度，不经过注册）
     *
     * @param taskName 任务名称
     * @param task     要执行的任务
     * @param initialDelay 初始延迟时间（毫秒）
     * @param delay    周期间隔（毫秒）
     * @return ScheduledFuture
     */
    public ScheduledFuture<?> scheduleWithFixedDelay(String taskName, Runnable task, long initialDelay, long delay) {
        if (taskName == null || taskName.isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (task == null) {
            throw new IllegalArgumentException("任务不能为null");
        }
        if (initialDelay < 0) {
            throw new IllegalArgumentException("初始延迟时间不能小于0");
        }
        if (delay <= 0) {
            throw new IllegalArgumentException("延迟时间必须大于0");
        }

        ScheduledFuture<?> scheduledFuture = scheduler.scheduleWithFixedDelay(task, initialDelay, delay, TimeUnit.MILLISECONDS);
        scheduledTasks.put(taskName, scheduledFuture);
        log.info("已调度固定延迟任务: {}，初始延迟: {}ms，周期: {}ms", taskName, initialDelay, delay);
        return scheduledFuture;
    }

    /**
     * 调度一次性延迟任务（直接调度，不经过注册）
     *
     * @param taskName 任务名称
     * @param task     要执行的任务
     * @param delay    延迟时间（毫秒）
     * @return ScheduledFuture
     */
    public ScheduledFuture<?> scheduleWithDelay(String taskName, Runnable task, long delay) {
        if (taskName == null || taskName.isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (task == null) {
            throw new IllegalArgumentException("任务不能为null");
        }
        if (delay < 0) {
            throw new IllegalArgumentException("延迟时间不能小于0");
        }

        ScheduledFuture<?> scheduledFuture = scheduler.schedule(task, delay, TimeUnit.MILLISECONDS);
        scheduledTasks.put(taskName, scheduledFuture);
        log.info("已调度延迟任务: {}，延迟: {}ms", taskName, delay);
        return scheduledFuture;
    }

    /**
     * 取消指定任务
     *
     * @param taskName 任务名称
     * @param mayInterruptIfRunning 是否中断正在运行的任务
     */
    public void cancelTask(String taskName, boolean mayInterruptIfRunning) {
        // 先尝试从已调度任务中取消
        ScheduledFuture<?> scheduledFuture = scheduledTasks.get(taskName);
        if (scheduledFuture != null) {
            scheduledFuture.cancel(mayInterruptIfRunning);
            scheduledTasks.remove(taskName);
            log.info("已取消已调度任务: {}", taskName);
            return;
        }

        // 再尝试从待注册任务中移除
        TaskConfig removed = pendingTasks.remove(taskName);
        if (removed != null) {
            log.info("已移除待注册任务: {}", taskName);
        }
    }

    /**
     * 检查任务是否存在（包括已调度和待注册的任务）
     *
     * @param taskName 任务名称
     * @return 是否存在
     */
    public boolean hasTask(String taskName) {
        return scheduledTasks.containsKey(taskName) || pendingTasks.containsKey(taskName);
    }

    /**
     * 获取任务状态
     *
     * @param taskName 任务名称
     * @return 任务是否已完成或已取消
     */
    public boolean isTaskDone(String taskName) {
        ScheduledFuture<?> scheduledFuture = scheduledTasks.get(taskName);
        return scheduledFuture == null || scheduledFuture.isDone();
    }

    /**
     * 检查TaskSchedulerManager是否已准备就绪
     * @return 是否已准备就绪
     */
    public boolean isReady() {
        return isReady;
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        // 取消所有已调度的任务
        scheduledTasks.forEach((taskName, scheduledFuture) -> {
            scheduledFuture.cancel(false);
            log.info("已取消已调度任务: {}", taskName);
        });
        scheduledTasks.clear();

        // 清理所有待注册的任务
        pendingTasks.clear();
        log.info("已清理所有待注册任务");

        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("TaskSchedulerManager已清理所有任务");
    }
}
