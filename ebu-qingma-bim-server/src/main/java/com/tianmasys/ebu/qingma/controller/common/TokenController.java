package com.tianmasys.ebu.qingma.controller.common;

import javax.servlet.http.HttpServletRequest;

import com.tianmasys.ebu.system.api.model.LoginUser;
import com.tianmasys.ebu.qingma.domain.LoginBody;
import com.tianmasys.ebu.qingma.domain.RegisterBody;
import com.tianmasys.ebu.qingma.service.SysLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.core.domain.R;
import com.tianmasys.ebu.common.core.utils.JwtUtils;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.common.security.auth.AuthUtil;
import com.tianmasys.ebu.common.security.service.TokenService;
import com.tianmasys.ebu.common.security.utils.SecurityUtils;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
public class TokenController
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @PostMapping("login")
    public R<?> login(@RequestBody LoginBody form)
    {
        sysLoginService.validateCaptcha(form.getUsername(), form.getCode(), form.getUuid());
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser))
        {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody)
    {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }
}
