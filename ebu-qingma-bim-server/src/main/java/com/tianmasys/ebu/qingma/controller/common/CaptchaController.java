package com.tianmasys.ebu.qingma.controller.common;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import com.google.code.kaptcha.Producer;
import com.tianmasys.ebu.common.core.constant.CacheConstants;
import com.tianmasys.ebu.common.core.constant.Constants;
import com.tianmasys.ebu.common.core.utils.sign.Base64;
import com.tianmasys.ebu.common.core.utils.uuid.IdUtils;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.redis.service.RedisService;
//import com.tianmasys.ebu.system.service.ISysConfigService;
import com.tianmasys.ebu.module.system.config.properties.CaptchaProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 验证码操作处理
 * 
 * <AUTHOR>
 */
@RestController
public class CaptchaController
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisService redisService;
    
//    @Autowired
//    private ISysConfigService configService;

    @Autowired
    private CaptchaProperties captchaProperties;
    /**
     * 生成验证码
     */
    @GetMapping("/code")
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {
//        AjaxResult ajax = AjaxResult.success();
////        boolean captchaEnabled = configService.selectCaptchaEnabled();
//        boolean captchaEnabled = true;
//        ajax.put("captchaEnabled", captchaEnabled);
//        if (!captchaEnabled)
//        {
//            return ajax;
//        }
//
//        // 保存验证码信息
//        String uuid = IdUtils.simpleUUID();
//        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
//
//        String capStr = null, code = null;
//        BufferedImage image = null;
//
//        // 生成验证码
////        String captchaType = RuoYiConfig.getCaptchaType();
//        String captchaType = "math";
//        if ("math".equals(captchaType))
//        {
//            String capText = captchaProducerMath.createText();
//            capStr = capText.substring(0, capText.lastIndexOf("@"));
//            code = capText.substring(capText.lastIndexOf("@") + 1);
//            image = captchaProducerMath.createImage(capStr);
//        }
//        else if ("char".equals(captchaType))
//        {
//            capStr = code = captchaProducer.createText();
//            image = captchaProducer.createImage(capStr);
//        }
//
////        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
//        redisCache.setCacheObject(verifyKey, code, 1L, TimeUnit.MINUTES);
//        // 转换流信息写出
//        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
//        try
//        {
//            ImageIO.write(image, "jpg", os);
//        }
//        catch (IOException e)
//        {
//            return AjaxResult.error(e.getMessage());
//        }
//
//        ajax.put("uuid", uuid);
//        ajax.put("captchaEnabled", uuid);
//        ajax.put("img", Base64.encode(os.toByteArray()));
//        return ajax;
        boolean captchaEnabled = captchaProperties.getEnabled();
//        boolean captchaEnabled = true;
        Map<String,Object> res = new HashMap<>();
        res.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled)
        {
            return AjaxResult.success(res);
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        String captchaType = captchaProperties.getType();
//        String captchaType = "math";
        // 生成验证码
        if ("math".equals(captchaType))
        {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        }
        else if ("char".equals(captchaType))
        {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisService.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return AjaxResult.error(e.getMessage());
        }
        res.put("uuid", uuid);
        res.put("img", Base64.encode(os.toByteArray()));

        return AjaxResult.success(res);
    }
}
