package com.tianmasys.ebu.qingma.dataprocess.modbus.config;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
public class MasterData {

    private String id;

    /**
     *  站点ID
     */
    private Integer siteId;

    /**
     * 连接地址
     */
    private String host;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 是否保持长连接
     */
    private Boolean keepAlive;

    /**
     * 从站数据
     */
    private List<SlaveData> slaves;

    /**
     * 获取从站数据配置
     * @param slaveId
     * @return
     */
    public SlaveData getSlaveData(Integer slaveId) {
        for (SlaveData slaveData : slaves) {
            if (slaveData.getSlaveId().equals(slaveId)) {
                return slaveData;
            }
        }
        return null;
    }
}
