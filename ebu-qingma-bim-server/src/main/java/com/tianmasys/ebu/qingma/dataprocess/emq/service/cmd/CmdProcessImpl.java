package com.tianmasys.ebu.qingma.dataprocess.emq.service.cmd;

import com.alibaba.fastjson2.JSONObject;
import com.tianmasys.ebu.common.core.utils.DateUtils;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.common.redis.service.RedisService;
import com.tianmasys.ebu.qingma.service.IDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CmdProcessImpl implements CmdProcess{

//    private Map<String,Object> map = new ConcurrentHashMap<>();

    @Autowired
    private RedisService redisService;

    @Autowired
    private IDeviceService deviceServiceImpl;

    @Override
    public void cmdProcess(String proId,JSONObject msg) {
        String cmd = msg.getString("cmd");

        if(("uploadStatus".equals(cmd) || "queryRunStatus".equals(cmd)) || "reboot".equals(cmd)){ //用户主动查询 或主动上报 设备重启

                JSONObject res = JSONObject.parseObject(msg.toString());
                String ct = StringUtils.nvl(res.getString("ct"),"");
                String[]  tmps = ct.split("\\|");

                if(tmps.length == 3){
                    res.put("csq",tmps[0]);
                    res.put("pinState",tmps[1]);
                    res.put("waterLevel",tmps[2]);
                    msg.putAll(res);
                    
                    JSONObject cache = new JSONObject();
                    cache.put("csq",tmps[0]);
                    cache.put("pinState",tmps[1]);
                    cache.put("waterLevel",tmps[2]);
                    cache.put("lastUpdateTime", DateUtils.getTime());
                    cache.put("devId", msg.getString("devId"));

                    redisService.setCacheMapValue("device_state:"+proId, msg.getString("devId"), cache);
                }
                String devId =  msg.getString("devId");
                deviceServiceImpl.deviceUpdateStatus(proId,devId,msg);

                //主动查询，放入到缓存
                if("queryRunStatus".equals(cmd)){
                    String key =  devId+"_"+cmd;
                    redisService.setCacheObject(key,msg,60L, TimeUnit.MICROSECONDS);
                }

        }else{
            String devId =  msg.getString("devId");
            String key =  devId+"_"+cmd;
            redisService.setCacheObject(key,msg,60L, TimeUnit.MICROSECONDS);
        }

    }


    public Object getMap(String key){
        if(!redisService.hasKey(key)){
            return null;
        }else{
            Object o = redisService.getCacheObject(key);
            redisService.deleteObject(key);
            return o;
        }
    }
}
