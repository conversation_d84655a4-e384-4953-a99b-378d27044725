package com.tianmasys.ebu.qingma.config;

import com.tianmasys.ebu.qingma.service.CommonCustService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class InitConfig {

    @Autowired
    private CommonCustService commonCustService;
    @PostConstruct
    public  void init(){
        commonCustService.clearAllCache();
    }
}
