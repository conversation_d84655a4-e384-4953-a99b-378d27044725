package com.tianmasys.ebu.qingma.statistics;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import com.tianmasys.ebu.qingma.domain.ModbusDataStatisticsEntity;
import com.tianmasys.ebu.qingma.service.IModbusDataLogService;
import com.tianmasys.ebu.qingma.service.IModbusDataStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Modbus统计数据处理器
 * 负责实时处理数据并按配置周期保存统计数据
 */
@Slf4j
@Component
public class ModbusStatisticsProcessor {

    // 共享的统计数据存储
    private final ModbusStatisticsData statisticsData = new ModbusStatisticsData();

    @Autowired
    private IModbusDataStatisticsService modbusDataStatisticsServiceImpl;

    @Autowired
    private IModbusDataLogService modbusDataLogService;

    // 用于跟踪每个站点每种统计类型上次保存的时间
    private final Map<String, Date> lastSaveTimeMap = new ConcurrentHashMap<>();

    // 记录第一次处理数据的时间
    private Date firstProcessTime;

    // 标记是否已经记录了第一次处理时间
    private final AtomicBoolean firstProcessTimeRecorded = new AtomicBoolean(false);

    // 记录每个统计类型上一次保存的周期开始时间，避免重复保存
    private final Map<String, Date> lastSavedPeriodStartMap = new ConcurrentHashMap<>();

    /**
     * 初始化处理器
     */
    @PostConstruct
    public void init() {
        log.info("Modbus统计数据处理器初始化完成");
    }

    /**
     * 处理新到达的数据
     * @param data JSONObject数据，key为siteId，value为数据
     */
    public void processNewData(JSONObject data) {
        try {
            Date now = new Date();

            // 记录第一次处理数据的时间
            if (firstProcessTimeRecorded.compareAndSet(false, true)) {
                firstProcessTime = now;
                log.info("记录第一次处理数据时间: {}", firstProcessTime);
            }

            // 遍历JSONObject中的每个站点数据
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String siteIdStr = entry.getKey();
                Object siteDataObj = entry.getValue();

                // 将siteId字符串转换为Long类型
                Long siteId;
                try {
                    siteId = Long.parseLong(siteIdStr);
                } catch (NumberFormatException e) {
                    log.warn("无效的站点ID格式: {}", siteIdStr);
                    continue;
                }

                // 确保站点数据是Map类型
                if (!(siteDataObj instanceof Map)) {
                    log.warn("站点{}的数据格式不正确", siteIdStr);
                    continue;
                }

                @SuppressWarnings("unchecked")
                Map<String, Object> siteData = (Map<String, Object>) siteDataObj;

                // 获取或创建站点统计数据
                ModbusStatisticsData.SiteStatisticsData siteStats = statisticsData.getSiteDataMap()
                        .computeIfAbsent(siteId, k -> {
                            ModbusStatisticsData.SiteStatisticsData ssd = new ModbusStatisticsData.SiteStatisticsData();
                            ssd.setSiteId(siteId);
                            return ssd;
                        });

                // 处理每个统计类型的数据 (minute, hour, day)
                processSiteData(siteStats, "minute", siteData, now);
                processSiteData(siteStats, "hour", siteData, now);
                processSiteData(siteStats, "day", siteData, now);

                // 每次有新数据时检查是否需要保存统计数据
                checkAndSaveOnDataArrival(siteId, now);
            }
        } catch (Exception e) {
            log.error("处理新数据时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理站点数据
     * @param siteStats 站点统计数据
     * @param statType 统计类型
     * @param data 数据
     * @param now 当前时间
     */
    private void processSiteData(ModbusStatisticsData.SiteStatisticsData siteStats, String statType, Map<String, Object> data, Date now) {
        ModbusStatisticsData.StatisticTypeData typeData = siteStats.getTypeDataMap()
                .computeIfAbsent(statType, k -> {
                    ModbusStatisticsData.StatisticTypeData std = new ModbusStatisticsData.StatisticTypeData();
                    std.setStatType(statType);
                    std.setStartTime(getCurrentPeriodStartTime(statType, now));
                    return std;
                });

        // 处理数据点
        Object dataObj = data.get("data");
        if (dataObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) dataObj;
            processDataPoints(typeData, dataMap);
        }
    }

    /**
     * 处理数据点
     * @param typeData 类型数据
     * @param dataMap 数据映射
     */
    @SuppressWarnings("unchecked")
    private void processDataPoints(ModbusStatisticsData.StatisticTypeData typeData, Map<String, Object> dataMap) {
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            Object valueObj = entry.getValue();

            if (valueObj instanceof Map) {
                // 处理复杂对象，如 {"unit": "m³/h", "trend": "up", "value": 23, "status": "normal"}
                Map<String, Object> valueMap = (Map<String, Object>) valueObj;
                Object value = valueMap.get("value");

                ModbusStatisticsData.DataPointStats dataPointStats = typeData.getDataPoints()
                        .computeIfAbsent(key, k -> {
                            ModbusStatisticsData.DataPointStats dps = new ModbusStatisticsData.DataPointStats();
                            dps.setKey(key);
                            return dps;
                        });

                dataPointStats.addValue(value);
                dataPointStats.setUnit(valueMap.get("unit"));
                dataPointStats.setStatus(valueMap.get("status"));
                dataPointStats.setTrend(valueMap.get("trend"));
            } else {
                // 处理简单值
                ModbusStatisticsData.DataPointStats dataPointStats = typeData.getDataPoints()
                        .computeIfAbsent(key, k -> {
                            ModbusStatisticsData.DataPointStats dps = new ModbusStatisticsData.DataPointStats();
                            dps.setKey(key);
                            return dps;
                        });

                dataPointStats.addValue(valueObj);
            }
        }
    }

    /**
     * 获取当前周期的开始时间
     * @param statType 统计类型 (minute, hour, day)
     * @param now 当前时间
     * @return 当前周期的开始时间
     */
    private Date getCurrentPeriodStartTime(String statType, Date now) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);

        switch (statType) {
            case "minute":
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                break;
            case "hour":
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                break;
            case "day":
                cal.set(Calendar.HOUR_OF_DAY, 0);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                break;
            default:
                return now;
        }

        return cal.getTime();
    }

    /**
     * 数据到达时检查是否需要保存统计数据
     * @param siteId 站点ID
     * @param now 当前时间
     */
    private void checkAndSaveOnDataArrival(Long siteId, Date now) {
        // 检查是否需要保存分钟统计数据（每分钟整点）
        checkAndSaveStatistics("minute", siteId, now);

        // 检查是否需要保存小时统计数据（每小时整点）
        checkAndSaveStatistics("hour", siteId, now);

        // 检查是否需要保存天统计数据（每天整点）
        checkAndSaveStatistics("day", siteId, now);
    }

    /**
     * 由外部触发的处理方法，每秒调用一次
     * 根据任务类型判断是否需要保存统计数据
     */
    public void processScheduledTask() {
        Date now = new Date();

        // 检查是否需要保存分钟统计数据（每分钟整点）
        checkAndSaveStatisticsForAllSites("minute", now);

        // 检查是否需要保存小时统计数据（每小时整点）
        checkAndSaveStatisticsForAllSites("hour", now);

        // 检查是否需要保存天统计数据（每天整点）
        checkAndSaveStatisticsForAllSites("day", now);
    }

    /**
     * 检查并保存指定类型的所有站点统计数据（由定时任务调用）
     * @param statType 统计类型
     * @param now 当前时间
     */
    private void checkAndSaveStatisticsForAllSites(String statType, Date now) {
        Map<Long, ModbusStatisticsData.SiteStatisticsData> siteDataMap = statisticsData.getSiteDataMap();

        for (Map.Entry<Long, ModbusStatisticsData.SiteStatisticsData> siteEntry : siteDataMap.entrySet()) {
            Long siteId = siteEntry.getKey();
            checkAndSaveStatistics(statType, siteId, now);
        }
    }

    /**
     * 检查并保存指定类型的统计数据
     * @param statType 统计类型
     * @param siteId 站点ID
     * @param now 当前时间
     */
    private void checkAndSaveStatistics(String statType, Long siteId, Date now) {
        // 检查是否到达整点
        if (isAtPeriodEnd(statType, now)) {
            String key = siteId + "_" + statType;

            // 获取当前周期的开始时间
            Date currentPeriodStart = getCurrentPeriodStartTime(statType, now);

            // 计算要保存的统计周期（上一个周期）
            Date statPeriodStart = getPreviousPeriodStartTime(statType, currentPeriodStart);
            Date statPeriodEnd = currentPeriodStart;

            // 检查是否已经保存过这个周期的数据
            Date lastSavedPeriodStart = lastSavedPeriodStartMap.get(key);
            if (lastSavedPeriodStart != null && lastSavedPeriodStart.equals(statPeriodStart)) {
                // 已经保存过这个周期的数据，不需要重复保存
                return;
            }

            // 补全数据并保存
            saveStatisticsWithCompletion(statType, siteId, now, statPeriodStart, statPeriodEnd);

            // 记录保存时间
            lastSaveTimeMap.put(key, now);
            lastSavedPeriodStartMap.put(key, statPeriodStart);
        }
    }

    /**
     * 判断当前时间是否为统计周期的整点
     * @param statType 统计类型
     * @param now 当前时间
     * @return 是否为整点
     */
    private boolean isAtPeriodEnd(String statType, Date now) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);

        switch (statType) {
            case "minute":
                // 每分钟的第0秒
                return cal.get(Calendar.SECOND) == 0;
            case "hour":
                // 每小时的0分0秒
                return cal.get(Calendar.MINUTE) == 0 && cal.get(Calendar.SECOND) == 0;
            case "day":
                // 每天的0点0分0秒
                return cal.get(Calendar.HOUR_OF_DAY) == 0 &&
                        cal.get(Calendar.MINUTE) == 0 &&
                        cal.get(Calendar.SECOND) == 0;
            default:
                return false;
        }
    }

    /**
     * 获取统计周期的毫秒数
     * @param statType 统计类型
     * @return 周期毫秒数
     */
    private long getPeriodInterval(String statType) {
        switch (statType) {
            case "minute":
                return 60 * 1000L; // 60秒
            case "hour":
                return 60 * 60 * 1000L; // 3600秒
            case "day":
                return 24 * 60 * 60 * 1000L; // 86400秒
            default:
                return 0;
        }
    }

    /**
     * 获取上一个周期的开始时间
     * @param statType 统计类型
     * @param currentPeriodStart 当前周期开始时间
     * @return 上一个周期的开始时间
     */
    private Date getPreviousPeriodStartTime(String statType, Date currentPeriodStart) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentPeriodStart);

        switch (statType) {
            case "minute":
                cal.add(Calendar.MINUTE, -1);
                break;
            case "hour":
                cal.add(Calendar.HOUR, -1);
                break;
            case "day":
                cal.add(Calendar.DAY_OF_MONTH, -1);
                break;
        }

        return cal.getTime();
    }

    /**
     * 补全数据并保存统计数据
     * @param statType 统计类型
     * @param siteId 站点ID
     * @param now 当前时间
     * @param periodStartTime 统计周期开始时间
     * @param periodEndTime 统计周期结束时间
     */
    private void saveStatisticsWithCompletion(String statType, Long siteId, Date now, Date periodStartTime, Date periodEndTime) {
        try {
            ModbusStatisticsData.SiteStatisticsData siteStats = statisticsData.getSiteDataMap().get(siteId);
            if (siteStats == null) {
                return;
            }

            ModbusStatisticsData.StatisticTypeData typeData = siteStats.getTypeDataMap().get(statType);
            if (typeData == null) {
                return;
            }

            // 如果内存中有数据且第一次处理时间后有缺失的数据，则从数据库补全
            if (typeData.getDataPoints().size() > 0 &&
                    firstProcessTime != null &&
                    periodStartTime.before(firstProcessTime)) {
                // 从数据库获取该周期的数据进行补全
                completeDataFromDatabase(statType, siteId, periodStartTime, periodEndTime, typeData);
            }

            // 构建统计数据
            Map<String, Object> statResult = new HashMap<>();
            for (Map.Entry<String, ModbusStatisticsData.DataPointStats> pointEntry : typeData.getDataPoints().entrySet()) {
                ModbusStatisticsData.DataPointStats pointStats = pointEntry.getValue();
                Map<String, Object> pointResult = new HashMap<>();
                double average = pointStats.getAverage();

                pointResult.put("avg", NumberUtil.round(average,1));
                pointResult.put("last", pointStats.getLastValue());
                pointResult.put("count", pointStats.getCount());

                // 计算趋势值
                String dataPointKey = pointEntry.getKey();
                Double previousAverage = typeData.getPreviousAverages().get(dataPointKey);
                if (previousAverage != null) {
                    if (average > previousAverage) {
                        pointResult.put("trend", "up");
                    } else if (average < previousAverage) {
                        pointResult.put("trend", "down");
                    } else {
                        pointResult.put("trend", "stable");
                    }
                }else {
                    pointResult.put("trend", "unkonw");
                }

                if (pointStats.getUnit() != null) {
                    pointResult.put("unit", pointStats.getUnit());
                }
                if (pointStats.getStatus() != null) {
                    pointResult.put("status", pointStats.getStatus());
                }
//                if (pointStats.getTrend() != null) {
//                    pointResult.put("trend", pointStats.getTrend());
//                }

                statResult.put(pointEntry.getKey(), pointResult);
            }

            // 创建统计数据实体
            ModbusDataStatisticsEntity entity = new ModbusDataStatisticsEntity();
            entity.setSiteId(siteId);
            entity.setStatType(statType);
            entity.setStartTime(periodStartTime);
            entity.setEndTime(periodEndTime);
            entity.setData(statResult);

            // 保存到数据库
            modbusDataStatisticsServiceImpl.save(entity);

            // 保存当前平均值作为下一次比较的基准
            for (Map.Entry<String, ModbusStatisticsData.DataPointStats> pointEntry : typeData.getDataPoints().entrySet()) {
                String dataPointKey = pointEntry.getKey();
                double currentAverage = pointEntry.getValue().getAverage();
                typeData.getPreviousAverages().put(dataPointKey, currentAverage);
            }

            // 重置统计数据
            for (ModbusStatisticsData.DataPointStats pointStats : typeData.getDataPoints().values()) {
                pointStats.reset();
            }

            // 更新下一个周期的开始时间
            typeData.setStartTime(periodEndTime);

            log.info("保存站点{}的{}统计数据成功，时间范围: {} - {}", siteId, statType, periodStartTime, periodEndTime);
        } catch (Exception e) {
            log.error("保存站点{}的{}统计数据时出错: {}", siteId, statType, e.getMessage(), e);
        }
    }

    /**
     * 从数据库补全数据
     * @param statType 统计类型
     * @param siteId 站点ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param typeData 统计数据
     */
    private void completeDataFromDatabase(String statType, Long siteId, Date startTime, Date endTime,
                                          ModbusStatisticsData.StatisticTypeData typeData) {
        try {
            log.info("开始从数据库补全站点{}的{}统计数据，时间范围: {} - {}", siteId, statType, startTime, endTime);

            // 查询该时间段内的数据
            // 这里可以根据实际需求进行调整，例如查询最近一段时间的数据
            // 为简化实现，我们假设从modbus_data_log表中获取数据进行补全

            // 注意：实际实现中需要根据具体业务逻辑来补全数据
            // 这里仅作为示例展示如何从数据库获取数据进行补全
        } catch (Exception e) {
            log.error("从数据库补全站点{}的{}统计数据时出错: {}", siteId, statType, e.getMessage(), e);
        }
    }

    /**
     * 获取当前内存中的统计数据
     * @return 统计数据
     */
    public ModbusStatisticsData getStatisticsData() {
        return statisticsData;
    }

    /**
     * 获取第一次处理数据的时间
     * @return 第一次处理数据的时间
     */
    public Date getFirstProcessTime() {
        return firstProcessTime;
    }
}