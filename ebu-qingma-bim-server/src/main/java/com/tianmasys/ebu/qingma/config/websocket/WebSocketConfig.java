package com.tianmasys.ebu.qingma.config.websocket;

import com.tianmasys.ebu.qingma.config.websocket.handler.WebSocketHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import javax.annotation.Resource;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Resource
    private WebSocketHandler webSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册 WebSocket 处理器，支持跨域和SockJS
        registry.addHandler(webSocketHandler, "/ws")
                .setAllowedOriginPatterns("*")// 允许所有来源的连接
                .withSockJS();  // 启用 SockJS 支持，允许从 HTTP 降级到 WebSocket
    }
}