package com.tianmasys.ebu.qingma.constant;

import lombok.Getter;

public class IotConst {

    /**
     * mqtt-事件类型
     */
    @Getter
    public enum MqttEventType {
        CONNECTED("客户端-建立连接", "client.connected"),
        DISCONNECTED("客户端-断开连接", "client.disconnected");

        private String name;

        private String code;

        MqttEventType(String name, String code) {
            this.name = name;
            this.code = code;
        }
    }
    /**
     * mqtt-事件类型
     */
    @Getter
    public enum DeviceEventType {
        DEVICE_UPLOAD_STATE("设备状态上报", "0"),
        DEVICE_REG("注册", "1"),
        DEVICE_OFFLINE("离线", "2"),
        DEVICE_ONLINE("上线", "3"),
        DEVICE_OFF_WATER("缺水报警", "4");

        private String name;

        private String code;

        DeviceEventType(String name, String code) {
            this.name = name;
            this.code = code;
        }

        public static String getNameByKey(String code){
            if (null == code) {
                return null;
            }
            for (DeviceEventType deviceEventType : DeviceEventType.values()) {
                if (code.equals(deviceEventType.code)) {
                    return deviceEventType.name;
                }
            }
            return null;
        }
    }

}
