package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSONObject;
import com.tianmasys.ebu.qingma.vo.DeviceCmdVo;
import com.tianmasys.ebu.qingma.vo.EmqxClientReqVo;
import com.tianmasys.ebu.qingma.vo.EmqxClientRespVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.DeviceVO;
import com.tianmasys.ebu.qingma.service.IDeviceService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * iot设备表Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-01-17
 */
@RestController
@RequestMapping("/device")
public class DeviceController extends BaseController
{
    @Autowired
    private IDeviceService deviceService;

    /**
     * 查询iot设备表列表
     */
    @RequiresPermissions("iot:device:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<DeviceVO>> list(DeviceVO deviceVO)
    {
        startPage();
        List<DeviceVO> list = deviceService.selectDeviceList(deviceVO);
        return success(getDataTable(list));
    }


    /**
     * 导出iot设备表列表
     */
    @RequiresPermissions("iot:device:export")
    @Log(title = "iot设备表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceVO deviceVO)
    {
        List<DeviceVO> list = deviceService.selectDeviceList(deviceVO);
        ExcelUtil<DeviceVO> util = new ExcelUtil<DeviceVO>(DeviceVO.class);
        util.exportExcel(response, list, "iot设备表数据");
    }

    /**
     * 获取iot设备表详细信息
     */
    @RequiresPermissions("iot:device:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<DeviceVO> getInfo(@PathVariable("id") Long id)
    {
        return success(deviceService.selectDeviceById(id));
    }

    /**
     * 新增iot设备表
     */
    @RequiresPermissions("iot:device:add")
    @Log(title = "iot设备表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<DeviceVO> add(@Valid @RequestBody DeviceVO deviceVO)
    {
        return toAjax(deviceService.insertDevice(deviceVO));
    }

    /**
     * 修改iot设备表
     */
    @RequiresPermissions("iot:device:edit")
    @Log(title = "iot设备表", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<DeviceVO> edit(@Valid @RequestBody DeviceVO deviceVO)
    {
        return toAjax(deviceService.updateDevice(deviceVO));
    }

    /**
     * 删除iot设备表
     */
    @RequiresPermissions("iot:device:remove")
    @Log(title = "iot设备表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<DeviceVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceService.deleteDeviceByIds(ids));
    }


    /**
     * mqtt 服务端事件处理 webhook
     */
    @PostMapping("/dealMqttEvent")
    public AjaxResult<?> dealMqttEvent(@RequestBody JSONObject req)
    {
        String topic = req.getString("topic");
        deviceService.dealMqttEvent(topic,req);
        return AjaxResult.success();
    }
    /**
     * 下发指令到设备
     */
    @PostMapping("/deviceCmd")
    public AjaxResult<?> deviceCmd(@RequestBody @Valid DeviceCmdVo deviceCmdVo)
    {
       JSONObject res = deviceService.deviceCmd(deviceCmdVo);
        return AjaxResult.success(res);
    }

    /**
     * 查询emqx客户端列表
     */
    @GetMapping("/qryEmqxClientList")
    public AjaxResult<EmqxClientRespVo> qryEmqxClientList( @Valid EmqxClientReqVo qmqxClientReqVo)
    {
        EmqxClientRespVo res = deviceService.qryEmqxClientList(qmqxClientReqVo);
        return AjaxResult.success(res);
    }
}
