package com.tianmasys.ebu.qingma.domain;

import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * iot设备事件表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-19
 */

@Data
@TableName(value ="iot_device_event")
public class DeviceEventEntity extends BaseEntity {
	/**
	* 设备 ID
	*/
	@TableId
	private Long id;

	/**
	* 设备id
	*/
	private Long deviceId;

	/**
	* 设备唯一标识符(全局唯一，用于识别设备)
	*/
	private String deviceKey;

	/**
	* 设备名称(在产品内唯一，用于标识设备)
	*/
	private String deviceName;

	/**
	* 设备备注名称
	*/
	private String nickname;

	/**
	* 产品编号
	*/
	private Long productId;

	/**
	* 产品标识
	*/
	private String productKey;

	/**
	* 设备类型:0-直连设备;1-网关子设备;2-网关设备;
	*/
	private String deviceType;

	/**
	* 事件类型:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;
	*/
	private String eventType;

	/**
	* 是否推送:0-不推送;1-推送;
	*/
	private String eventIsPush;

	/**
	* 模板消息id
	*/
	private Long msgTemplateId;

	/**
	* 事件报文
	*/
	private String eventMsg;

	/**
	* 事件时间
	*/
	private Date eventTime;

//	/**
//	* 是否删除：0-正常；1-删除；
//	*/
//	private String deleteFlag;
//
//	/**
//	* 创建时间
//	*/
//	private Date createTime;
//
//	/**
//	* 创建用户id
//	*/
//	private String createBy;
//
//	/**
//	* 修改时间
//	*/
//	private Date updateTime;
//
//	/**
//	* 修改用户id
//	*/
//	private String updateBy;
//
//	/**
//	* 租户编号
//	*/
//	private Long tenantId;

	/**
	 * 产品名称
	 */
	@TableField(exist = false)
	private String productName;


}