package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tianmasys.ebu.qingma.vo.SubCustVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.CustRelationVO;
import com.tianmasys.ebu.qingma.service.ICustRelationService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * 客户关联关系Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-02-21
 */
@RestController
@RequestMapping("/custRelation")
public class CustRelationController extends BaseController
{
    @Autowired
    private ICustRelationService custRelationService;

    /**
     * 查询客户关联关系列表
     */
    @RequiresPermissions("iot:custRelation:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<CustRelationVO>> list(CustRelationVO custRelationVO)
    {
        startPage();
        List<CustRelationVO> list = custRelationService.selectCustRelationList(custRelationVO);
        return success(getDataTable(list));
    }


    /**
     * 导出客户关联关系列表
     */
    @RequiresPermissions("iot:custRelation:export")
    @Log(title = "客户关联关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CustRelationVO custRelationVO)
    {
        List<CustRelationVO> list = custRelationService.selectCustRelationList(custRelationVO);
        ExcelUtil<CustRelationVO> util = new ExcelUtil<CustRelationVO>(CustRelationVO.class);
        util.exportExcel(response, list, "客户关联关系数据");
    }

    /**
     * 获取客户关联关系详细信息
     */
    @RequiresPermissions("iot:custRelation:query")
    @GetMapping(value = "/{custId}")
    public AjaxResult<CustRelationVO> getInfo(@PathVariable("custId") String custId)
    {
        return success(custRelationService.selectCustRelationByCustId(custId));
    }

    /**
     * 新增客户关联关系
     */
    @RequiresPermissions("iot:custRelation:add")
    @Log(title = "客户关联关系", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<CustRelationVO> add(@Valid @RequestBody CustRelationVO custRelationVO)
    {
        return toAjax(custRelationService.insertCustRelation(custRelationVO));
    }

    /**
     * 修改客户关联关系
     */
    @RequiresPermissions("iot:custRelation:edit")
    @Log(title = "客户关联关系", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<CustRelationVO> edit(@Valid @RequestBody CustRelationVO custRelationVO)
    {
        return toAjax(custRelationService.updateCustRelation(custRelationVO));
    }

    /**
     * 删除客户关联关系
     */
    @RequiresPermissions("iot:custRelation:remove")
    @Log(title = "客户关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{custIds}")
    public AjaxResult<CustRelationVO> remove(@PathVariable String[] custIds)
    {
        return toAjax(custRelationService.deleteCustRelationByCustIds(custIds));
    }


    /**
     * 新增客户关联关系
     */
    @RequiresPermissions("iot:custRelation:add")
    @Log(title = "添加子账号", businessType = BusinessType.INSERT)
    @PostMapping("/addSubCust")
    public AjaxResult<CustRelationVO> addSubCust(@Valid @RequestBody SubCustVo subCustVo)
    {
        return toAjax(custRelationService.addSubCust(subCustVo));
    }
}
