package com.tianmasys.ebu.qingma.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ProductMapper;
import com.tianmasys.ebu.qingma.service.IProductService;
import com.tianmasys.ebu.qingma.vo.ProductVO;
import com.tianmasys.ebu.qingma.domain.ProductEntity;

/**
 * iot产品表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */

@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, ProductEntity> implements IProductService {
    @Autowired
    private ProductMapper productMapper;

    /**
     * 查询iot产品表详情
     *
     * @param id iot产品表主键
     * @return iot产品表
     */
    @Override
    public ProductVO selectProductById(Long id) {
        ProductEntity entity = productMapper.selectById(id);
        return BeanUtil.toBean(entity, ProductVO.class);
    }

    /**
     * 查询iot产品表列表
     *
     * @param productVO iot产品表
     * @return iot产品表
     */
    @Override
    public List<ProductVO> selectProductList(ProductVO productVO) {
        List<ProductEntity> productEntities = productMapper.selectList(getWrapper(productVO));
        return PageObjectConvertUtil.convert(productEntities, ProductVO.class);
    }


    /**
     * 构造查询器
     */
    private LambdaQueryWrapper<ProductEntity> getWrapper(ProductVO query) {
        LambdaQueryWrapper<ProductEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(ObjUtil.isNotEmpty(query.getName()), ProductEntity::getName, query.getName());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProductKey()), ProductEntity::getProductKey, query.getProductKey());
        wrapper.eq(ObjUtil.isNotEmpty(query.getCategoryId()), ProductEntity::getCategoryId, query.getCategoryId());
        wrapper.like(ObjUtil.isNotEmpty(query.getDescription()), ProductEntity::getDescription, query.getDescription());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStatus()), ProductEntity::getStatus, query.getStatus());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceType()), ProductEntity::getDeviceType, query.getDeviceType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getNetType()), ProductEntity::getNetType, query.getNetType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProtocolType()), ProductEntity::getProtocolType, query.getProtocolType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProtocolId()), ProductEntity::getProtocolId, query.getProtocolId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDataFormat()), ProductEntity::getDataFormat, query.getDataFormat());
        wrapper.eq(ObjUtil.isNotEmpty(query.getValidateType()), ProductEntity::getValidateType, query.getValidateType());
        return wrapper;
    }

    /**
     * 新增iot产品表
     *
     * @param productVO iot产品表
     * @return 结果
     */
    @Override
    public int insertProduct(ProductVO productVO) {
        ProductEntity entity = BeanUtil.toBean(productVO, ProductEntity.class);
        entity.setProductKey(RandomUtil.randomString(16));
        return productMapper.insert(entity);
    }

    /**
     * 修改iot产品表
     *
     * @param productVO iot产品表
     * @return 结果
     */
    @Override
    public int updateProduct(ProductVO productVO) {
        ProductEntity entity = BeanUtil.toBean(productVO, ProductEntity.class);
        return productMapper.updateById(entity);
    }

    /**
     * 批量删除iot产品表
     *
     * @param ids 需要删除的iot产品表主键
     * @return 结果
     */
    @Override
    public int deleteProductByIds(Long[] ids) {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return productMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除iot产品表信息
     *
     * @param id iot产品表主键
     * @return 结果
     */
    @Override
    public int deleteProductById(Long id) {
        return productMapper.deleteById(id);
    }

}
