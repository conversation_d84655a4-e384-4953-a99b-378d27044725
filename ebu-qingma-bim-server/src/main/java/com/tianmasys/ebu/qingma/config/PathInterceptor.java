package com.tianmasys.ebu.qingma.config;

import cn.hutool.core.text.AntPathMatcher;
import com.tianmasys.ebu.qingma.config.properties.RouteConfigProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Component
public class PathInterceptor implements HandlerInterceptor {

    @Autowired
    private RouteConfigProperties routeConfigProperties;



    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String path = request.getRequestURI();
        List<RouteConfig> routes = routeConfigProperties.getRoutes();

        for (RouteConfig route : routes) {

//            if (path.startsWith(route.getPathPrefix())) {
            if (antPathMatch(route.getPathPrefix(),path)) {
                String newURI = route.getUri() + path.substring(path.indexOf(route.getPrefixToRemove())+route.getPrefixToRemove().length());
                request.getRequestDispatcher(newURI).forward(request, response);
                return false;
            }
        }
        return true;
    }


    private boolean antPathMatch(String pattern, String path){
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        boolean match = antPathMatcher.match(pattern, path);
//        System.out.println("匹配规则="+pattern+"，匹配路径="+path+",匹配结果=" + match);
        return match;
    }

}
