package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
@Data
public class SubCustVo implements Serializable {

    @Schema(description = "父客户id")
    public String parentCustId;

    @Schema(description = "层级：0-父级;1-一级;2-二级;")
    @NotNull(message = "层级不能为空!")
    private Integer custLeave;

    @Schema(description = "用户名")
    @NotNull(message = "用户名不能为空!")
    public String username;

    @Schema(description = "密码")
    @NotNull(message = "密码不能为空!")
    public String password;

    @Schema(description = "手机号码")
    @NotNull(message = "手机号码不能为空!")
    public String mobile;

    @Schema(description = "应用id")
    @NotNull(message = "应用id不能为空!")
    public String appId;

    @Schema(description = "昵称")
    public String nickName;

    @Schema(description = "用户头像")
    public String headerUrl;
}
