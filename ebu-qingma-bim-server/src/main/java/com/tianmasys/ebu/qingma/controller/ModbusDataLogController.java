package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.ModbusDataLogVO;
import com.tianmasys.ebu.qingma.service.IModbusDataLogService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * modbus点位数据记录表Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-08-06
 */
@RestController
@RequestMapping("/modbusDataLog")
public class ModbusDataLogController extends BaseController
{
    @Autowired
    private IModbusDataLogService modbusDataLogService;

    /**
     * 查询modbus点位数据记录表列表
     */
    @RequiresPermissions("qingma:modbusDataLog:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<ModbusDataLogVO>> list(ModbusDataLogVO modbusDataLogVO)
    {
        startPage();
        List<ModbusDataLogVO> list = modbusDataLogService.selectModbusDataLogList(modbusDataLogVO);
        return success(getDataTable(list));
    }


    /**
     * 导出modbus点位数据记录表列表
     */
    @RequiresPermissions("qingma:modbusDataLog:export")
    @Log(title = "modbus点位数据记录表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModbusDataLogVO modbusDataLogVO)
    {
        List<ModbusDataLogVO> list = modbusDataLogService.selectModbusDataLogList(modbusDataLogVO);
        ExcelUtil<ModbusDataLogVO> util = new ExcelUtil<ModbusDataLogVO>(ModbusDataLogVO.class);
        util.exportExcel(response, list, "modbus点位数据记录表数据");
    }

    /**
     * 获取modbus点位数据记录表详细信息
     */
    @RequiresPermissions("qingma:modbusDataLog:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<ModbusDataLogVO> getInfo(@PathVariable("id") Long id)
    {
        return success(modbusDataLogService.selectModbusDataLogById(id));
    }

    /**
     * 新增modbus点位数据记录表
     */
    @RequiresPermissions("qingma:modbusDataLog:add")
    @Log(title = "modbus点位数据记录表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<ModbusDataLogVO> add(@Valid @RequestBody ModbusDataLogVO modbusDataLogVO)
    {
        return toAjax(modbusDataLogService.insertModbusDataLog(modbusDataLogVO));
    }

    /**
     * 修改modbus点位数据记录表
     */
    @RequiresPermissions("qingma:modbusDataLog:edit")
    @Log(title = "modbus点位数据记录表", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<ModbusDataLogVO> edit(@Valid @RequestBody ModbusDataLogVO modbusDataLogVO)
    {
        return toAjax(modbusDataLogService.updateModbusDataLog(modbusDataLogVO));
    }

    /**
     * 删除modbus点位数据记录表
     */
    @RequiresPermissions("qingma:modbusDataLog:remove")
    @Log(title = "modbus点位数据记录表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<ModbusDataLogVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(modbusDataLogService.deleteModbusDataLogByIds(ids));
    }
}
