package com.tianmasys.ebu.qingma.service;

import java.util.List;

import com.tianmasys.ebu.qingma.domain.ModbusConfigData;
import com.tianmasys.ebu.qingma.vo.ModbusSiteVO;
import com.tianmasys.ebu.qingma.domain.ModbusSiteEntity;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * modbus站点Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */
public interface IModbusSiteService extends IService<ModbusSiteEntity>
{
    /**
     * 查询modbusSite
     * 
     * @param id modbusSite主键
     * @return modbusSite
     */
    public ModbusSiteVO selectModbusSiteById(Long id);

    /**
     * 查询modbusSite列表
     * 
     * @param modbusSiteVO modbusSite
     * @return modbusSite集合
     */
    public List<ModbusSiteVO> selectModbusSiteList(ModbusSiteVO modbusSiteVO);

    /**
     * 新增modbusSite
     * 
     * @param modbusSiteVO modbusSite
     * @return 结果
     */
    public int insertModbusSite(ModbusSiteVO modbusSiteVO);

    /**
     * 修改modbusSite
     * 
     * @param modbusSiteVO modbusSite
     * @return 结果
     */
    public int updateModbusSite(ModbusSiteVO modbusSiteVO);

    /**
     * 批量删除modbusSite
     * 
     * @param ids 需要删除的modbusSite主键集合
     * @return 结果
     */
    public int deleteModbusSiteByIds(Long[] ids);

    /**
     * 删除modbusSite信息
     * 
     * @param id modbusSite主键
     * @return 结果
     */
    public int deleteModbusSiteById(Long id);

    /**
     *  获取modbus站点列表相关配置
     * @return
     */
    public List<ModbusConfigData> getModbusConfigDataList();

}
