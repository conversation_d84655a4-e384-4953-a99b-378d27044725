<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tianmasys.ebu.qingma.mapper.ThinkModelFunctionMapper">

    <resultMap type="com.tianmasys.ebu.qingma.domain.ThinkModelFunctionEntity" id="thinkModelFunctionMap">
        <result property="id" column="id"/>
        <result property="identifier" column="identifier"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="productId" column="product_id"/>
        <result property="productKey" column="product_key"/>
        <result property="type" column="type"/>
        <result property="property" column="property"/>
        <result property="event" column="event"/>
        <result property="service" column="service"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

</mapper>