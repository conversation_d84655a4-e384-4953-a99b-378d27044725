<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tianmasys.ebu.qingma.mapper.CustRelationMapper">

    <resultMap type="com.tianmasys.ebu.qingma.domain.CustRelationEntity" id="custRelationMap">
        <result property="custId" column="cust_id"/>
        <result property="parentCustId" column="parent_cust_id"/>
        <result property="custLeave" column="cust_leave"/>
        <result property="appId" column="app_id"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    <resultMap type="com.tianmasys.ebu.qingma.domain.CustRelationEntity" id="custRelationMapAll">
        <result property="custId" column="cust_id"/>
        <result property="parentCustId" column="parent_cust_id"/>
        <result property="custLeave" column="cust_leave"/>
        <result property="appId" column="app_id"/>
        <result property="userName" column="user_name"/>
        <result property="parentUserName" column="parent_user_name"/>
        <result property="mobile" column="mobile"/>
        <result property="parentMobile" column="parent_mobile"/>
        <result property="nickName" column="nick_name"/>
        <result property="parentNickName" column="parent_nick_name"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="selectCustRelationList" parameterType="com.tianmasys.ebu.qingma.domain.CustRelationEntity" resultMap="custRelationMapAll">
        SELECT
            t1.*,
            t2.eca_user_name as user_name,
            t3.eca_user_name as parent_user_name,
            t4.sai_mobile as mobile,
            t5.sai_mobile as parent_mobile,
            t4.sai_nick_name as nick_name,
            t5.sai_nick_name as parent_nick_name
        from iot_cust_relation t1
        LEFT JOIN ebu_cust_auth t2 on  t1.cust_id=t2.eca_cust_id
        LEFT JOIN ebu_cust_auth t3 on t1.parent_cust_id=t3.eca_cust_id
        LEFT JOIN ebu_cust_base_info t4 on  t1.cust_id=t4.sai_cust_id
        LEFT JOIN ebu_cust_base_info t5 on t1.parent_cust_id=t5.sai_cust_id
        <where>
            <if test="custRelation.custLeave != null"> and cust_leave = #{custRelation.custLeave}</if>
            <if test="custRelation.appId != null  and custRelation.appId != ''"> and app_id = #{custRelation.appId}</if>
            <if test="custRelation.parentCustId != null  and custRelation.parentCustId != ''"> and parent_cust_id = #{custRelation.parentCustId}</if>
            <if test="custIdList != null  and custIdList.size() > 0"> and cust_id in
                <foreach collection="custIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>
    <select id="selectCustRelationByCustId" parameterType="String" resultMap="custRelationMapAll">
        SELECT
            t1.*,
            t2.eca_user_name as user_name,
            t3.eca_user_name as parent_user_name,
            t4.sai_mobile as mobile,
            t5.sai_mobile as parent_mobile,
            t4.sai_nick_name as nick_name,
            t5.sai_nick_name as parent_nick_name
        from iot_cust_relation t1
        LEFT JOIN ebu_cust_auth t2 on  t1.cust_id=t2.eca_cust_id
        LEFT JOIN ebu_cust_auth t3 on t1.parent_cust_id=t3.eca_cust_id
        LEFT JOIN ebu_cust_base_info t4 on  t1.cust_id=t4.sai_cust_id
        LEFT JOIN ebu_cust_base_info t5 on t1.parent_cust_id=t5.sai_cust_id
        where cust_id = #{custId}
    </select>

    <select id="selectSubCustIdsByCustId" resultType="java.lang.String">
        select t.cust_id from (
        WITH RECURSIVE temp as (
            SELECT t.* FROM iot_cust_relation t WHERE t.cust_id = #{custId}
            UNION ALL
            SELECT t.* FROM iot_cust_relation t INNER JOIN temp ON t.parent_cust_id = temp.cust_id
        )
        SELECT * FROM temp) t;

    </select>
    <select id="selectParentCustIdsByCustId" resultType="java.lang.String">
        select t.cust_id from (
        WITH RECURSIVE temp as (
            SELECT t.* FROM iot_cust_relation t WHERE t.cust_id = #{custId}
            UNION ALL
            SELECT t.* FROM iot_cust_relation t INNER JOIN temp ON t.cust_id = temp.parent_cust_id
        )
        SELECT * FROM temp) t;

    </select>
</mapper>