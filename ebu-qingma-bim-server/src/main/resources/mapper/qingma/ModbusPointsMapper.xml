<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tianmasys.ebu.qingma.mapper.ModbusPointsMapper">

    <resultMap type="com.tianmasys.ebu.qingma.domain.ModbusPointsEntity" id="modbusPointsMap">
        <result property="id" column="id"/>
        <result property="pointName" column="point_name"/>
        <result property="description" column="description"/>
        <result property="siteId" column="site_id"/>
        <result property="masterId" column="master_id"/>
        <result property="slaveId" column="slave_id"/>
        <result property="functionCode" column="function_code"/>
        <result property="registerAddress" column="register_address"/>
        <result property="status" column="status"/>
        <result property="autoRead" column="auto_read"/>
        <result property="interval" column="interval"/>
        <result property="dataType" column="data_type"/>
        <result property="mappingConfig" column="mapping_config"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

</mapper>