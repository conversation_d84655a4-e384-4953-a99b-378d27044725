<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tianmasys.ebu</groupId>
        <artifactId>ebu-qingma-bim</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>ebu-qingma-bim-server</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <freemarker.version>2.3.31</freemarker.version>
    </properties>

    <dependencies>

        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>


        <!-- Spring Boot Starter Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- Alibaba Fastjson -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2-extension-spring5</artifactId>
        </dependency>


        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
        </dependency>

        <!-- 校验 -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-common-valid</artifactId>
        </dependency>

        <!-- ebu Common DataSource -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-common-datasource</artifactId>
        </dependency>

        <!-- ebu Common DataScope -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-common-datascope</artifactId>
        </dependency>

        <!-- ebu Common Log -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-common-log</artifactId>
        </dependency>

        <!-- ebu Common Swagger -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-common-swagger</artifactId>
        </dependency>

        <!--验证码 -->
        <dependency>
            <groupId>pro.fessional</groupId>
            <artifactId>kaptcha</artifactId>
        </dependency>

        <!-- ebu Module System -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-module-system</artifactId>
        </dependency>

        <!-- ebu Module Portal -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-module-portal</artifactId>
        </dependency>

        <!-- ebu Common Mybatis -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-common-mybatis</artifactId>
        </dependency>

        <!-- ebu Module file -->
        <dependency>
            <groupId>com.tianmasys.ebu</groupId>
            <artifactId>ebu-module-file</artifactId>
        </dependency>


        <!-- MQTT -->
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        </dependency>


      <!--  <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>-->

        <!-- Modbus4j -->
        <dependency>
            <groupId>com.infiniteautomation</groupId>
            <artifactId>modbus4j</artifactId>
            <version>3.1.0</version>
        </dependency>

        <!-- websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ibeetl</groupId>
            <artifactId>beetl</artifactId>
            <version>3.19.2.RELEASE</version>
        </dependency>

</dependencies>

<!-- maven多环境配置开始 -->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <!-- 默认情况下使用dev开发配置 如 打包时不包含 -P 参数-->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <finalName>${project.artifactId}</finalName>
                    <mainClass>com.tianmasys.ebu.qingma.EbuQingmaApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <!-- 先指定 src/main/resources下所有文件及文件夹为资源文件 -->
            <resource>
                <directory>src/main/resources</directory>
                <targetPath>${project.build.directory}/classes</targetPath>
                <includes>
                    <include>**/*</include>
                </includes>
                <excludes>
                    <exclude>env/**</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <!-- 根据env部署环境值，把对应环境的配置文件拷贝到classes目录 -->
            <resource>
                <directory>src/main/resources/env/${env}</directory>
                <targetPath>${project.build.directory}/classes</targetPath>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>




</project>